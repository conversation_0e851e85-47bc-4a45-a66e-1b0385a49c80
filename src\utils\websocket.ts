import { io, Socket } from "socket.io-client";

interface WebSocketOptions {
  onConnect?: () => void;
  onDisconnect?: (reason: string) => void;
  onError?: (error: Error) => void;
  onReconnect?: (attemptNumber: number) => void;
  onMarketData?: (data: any) => void;
  onMarketDataBatch?: (data: any[]) => void;
}

// Global socket instance to prevent multiple connections
let globalSocket: Socket | null = null;
let connectionCount = 0;

export const createWebSocketConnection = (
  options: WebSocketOptions = {}
): Socket => {
  // Reuse existing connection if available and connected
  if (globalSocket && globalSocket.connected) {
    console.log(
      `♻️ Reusing existing WebSocket connection (${++connectionCount} clients)`
    );

    // Add new listeners without creating new connection
    if (options.onMarketData) {
      globalSocket.on("marketData", options.onMarketData);
    }
    if (options.onMarketDataBatch) {
      globalSocket.on("marketDataBatch", options.onMarketDataBatch);
    }

    return globalSocket;
  }

  // Disconnect existing socket if it exists but is not connected
  if (globalSocket) {
    console.log("🔌 Cleaning up disconnected socket");
    globalSocket.disconnect();
  }

  console.log("🚀 Creating NEW WebSocket connection to backend");
  connectionCount = 1;

  const socketInstance = io("http://localhost:8080", {
    transports: ["websocket"],
    upgrade: false,
    rememberUpgrade: false,
    timeout: 10000,
    forceNew: false,
    reconnection: true,
    reconnectionAttempts: 2, // Reduced further
    reconnectionDelay: 3000,
    reconnectionDelayMax: 5000,
  });

  globalSocket = socketInstance;

  socketInstance.on("connect", () => {
    console.log(`✅ Connected to real-time feed (${connectionCount} clients)`);
    options.onConnect?.();
  });

  socketInstance.on("disconnect", (reason) => {
    console.log(`❌ Disconnected from real-time feed: ${reason}`);
    connectionCount = Math.max(0, connectionCount - 1);
    options.onDisconnect?.(reason);

    // Don't auto-reconnect - let the backend handle it
    if (reason === "io server disconnect") {
      console.log(
        "🔄 Server initiated disconnect - will reconnect automatically"
      );
    }
  });

  socketInstance.on("connect_error", (error) => {
    console.error("❌ WebSocket connection error:", error);
    options.onError?.(error);
  });

  socketInstance.on("reconnect", (attemptNumber) => {
    console.log(`🔄 Reconnected after ${attemptNumber} attempts`);
    options.onReconnect?.(attemptNumber);
  });

  if (options.onMarketData) {
    socketInstance.on("marketData", options.onMarketData);
  }

  if (options.onMarketDataBatch) {
    socketInstance.on("marketDataBatch", options.onMarketDataBatch);
  }

  return socketInstance;
};

// Function to properly cleanup WebSocket connection
export const cleanupWebSocketConnection = (): void => {
  if (globalSocket) {
    console.log("🧹 Cleaning up WebSocket connection");
    globalSocket.removeAllListeners();
    globalSocket.disconnect();
    globalSocket = null;
    connectionCount = 0;
  }
};

// Function to get connection status
export const getWebSocketStatus = (): {
  connected: boolean;
  clients: number;
} => {
  return {
    connected: globalSocket?.connected || false,
    clients: connectionCount,
  };
};
