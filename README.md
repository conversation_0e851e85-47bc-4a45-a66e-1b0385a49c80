# Dhan WebSocket Server

A high-performance market data server for the Dhan trading platform, built with Next.js, TypeScript, and WebSocket.

## Features

- Real-time market data streaming
- Multiple exchange support (NSE, BSE, MCX)
- WebSocket-based communication
- PostgreSQL database integration
- File upload and processing
- Comprehensive error handling
- Security features
- Logging system
- Performance optimizations

## Prerequisites

- Node.js 22.x or higher
- PostgreSQL 14.x or higher
- pnpm 9.x or higher

## Installation

1. Clone the repository:

   ```bash
   git clone https://github.com/yourusername/dhan-websocket-server.git
   cd dhan-websocket-server
   ```

2. Install dependencies:

   ```bash
   pnpm install
   ```

3. Create a `.env` file in the root directory with the following variables:

   ```env
   # Database Configuration
   POSTGRES_DATABASE_URL=postgresql://user:password@localhost:5432/dhan_db

   # API Credentials
   ACCESS_TOKEN=your_access_token_here
   CLIENT_ID=your_client_id_here

   # Server Configuration
   PORT=3000
   NODE_ENV=development

   # WebSocket Configuration
   SUBSCRIPTION_TYPE=quote
   MAX_INSTRUMENTS=25000
   PREFERRED_EXCHANGE=NSE

   # Security
   ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com
   RATE_LIMIT_WINDOW_MS=900000
   RATE_LIMIT_MAX_REQUESTS=100

   # File Upload
   MAX_FILE_SIZE=5242880
   UPLOAD_DIR=uploads

   # Logging
   LOG_LEVEL=info
   ```

4. Set up the database:

   ```bash
   # Create the database
   createdb dhan_db

   # Run migrations (if available)
   pnpm run migrate
   ```

## Development

1. Start the development server:

   ```bash
   pnpm run dev
   ```

   This will start both the Next.js frontend and the WebSocket server concurrently.

2. Access the application:
   - Frontend: http://localhost:3000
   - WebSocket Server: ws://localhost:3001

## Production

1. Build the application:

   ```bash
   pnpm run build
   ```

2. Start the production server:
   ```bash
   pnpm start
   ```

## Project Structure

```
dhan-websocket-server/
├── src/
│   ├── app/                 # Next.js app directory
│   ├── components/          # React components
│   ├── config/             # Configuration files
│   ├── lib/                # Utility functions
│   ├── middleware/         # Express middleware
│   ├── server/             # WebSocket server
│   ├── services/           # Business logic
│   └── types/              # TypeScript types
├── public/                 # Static files
├── uploads/               # File upload directory
└── logs/                 # Application logs
```

## API Documentation

### WebSocket Endpoints

- `ws://localhost:3001/market-data`
  - Subscribe to real-time market data
  - Message format: `{ type: string, data: any }`

### REST Endpoints

- `GET /api/sectors`

  - Get available sectors
  - Response: `{ success: boolean, data: string[] }`

- `GET /api/stock/:ticker`
  - Get stock data by ticker
  - Response: `{ success: boolean, data: StockData }`

## Security Features

- Rate limiting
- CORS protection
- Security headers
- Request validation
- File upload validation
- API key authentication
- Input sanitization

## Error Handling

The application uses a centralized error handling system with custom error classes and middleware. All errors are logged and formatted consistently.

## Logging

Logs are written to both the console and files in the `logs` directory. Log levels can be configured in the `.env` file.

## Performance Optimizations

- Connection pooling
- Request batching
- Caching
- Compression
- Rate limiting

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support, please open an issue in the GitHub repository or contact the maintainers.
