"use client";

import React, { useState } from "react";
import SimpleDashboard from "../components/SimpleDashboard";
import KeyIndicesComponent from "../components/KeyIndicesComponent";
import Navigation from "../components/Navigation";

export default function Home() {
  const [activeTab, setActiveTab] = useState<'dashboard' | 'indices'>('dashboard');

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20">
      <Navigation />
      <main className="relative">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5 pointer-events-none" />

        {/* Tab Navigation */}
        <div className="relative z-10 pt-6 px-6">
          <div className="flex space-x-4 mb-6">
            <button
              onClick={() => setActiveTab('dashboard')}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                activeTab === 'dashboard'
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'bg-white text-gray-700 hover:bg-gray-50 shadow-md'
              }`}
            >
              Market Dashboard
            </button>
            <button
              onClick={() => setActiveTab('indices')}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                activeTab === 'indices'
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'bg-white text-gray-700 hover:bg-gray-50 shadow-md'
              }`}
            >
              Key Indices
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="relative z-10 px-6 pb-6">
          {activeTab === 'dashboard' && <SimpleDashboard />}
          {activeTab === 'indices' && <KeyIndicesComponent />}
        </div>
      </main>
    </div>
  );
}
