// ============================================================================
// DATABASE SCHEMA CHECKER - Check available columns and data
// ============================================================================

import { Pool } from "pg";
import "dotenv/config";

// Parse PostgreSQL URL
function parsePostgresConfig() {
  const postgresUrl = process.env.POSTGRES_DATABASE_URL;

  if (postgresUrl) {
    const url = new URL(postgresUrl);
    return {
      host: url.hostname,
      port: parseInt(url.port) || 5432,
      database: url.pathname.slice(1),
      user: url.username,
      password: url.password,
      ssl: { rejectUnauthorized: false },
    };
  }

  throw new Error("POSTGRES_DATABASE_URL not found");
}

async function checkDatabaseSchema() {
  const config = parsePostgresConfig();
  const pool = new Pool(config);

  try {
    console.log("🔍 Connecting to database...");

    // Check if company_list table exists and get its structure
    const tableInfoQuery = `
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'company_list'
      ORDER BY ordinal_position;
    `;

    const tableInfo = await pool.query(tableInfoQuery);

    if (tableInfo.rows.length === 0) {
      console.log(
        "❌ Company_list table not found. Checking available tables..."
      );

      const tablesQuery = `
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        ORDER BY table_name;
      `;

      const tables = await pool.query(tablesQuery);
      console.log("📋 Available tables:");
      tables.rows.forEach((row) => console.log(`  - ${row.table_name}`));
    } else {
      console.log("✅ Company_list table found with columns:");
      tableInfo.rows.forEach((row) => {
        console.log(
          `  - ${row.column_name} (${row.data_type}) ${row.is_nullable === "YES" ? "NULL" : "NOT NULL"}`
        );
      });

      // Get sample data
      console.log("\n📊 Sample data (first 5 rows):");
      const sampleQuery = "SELECT * FROM company_list LIMIT 5";
      const sampleData = await pool.query(sampleQuery);

      if (sampleData.rows.length > 0) {
        console.log("Columns:", Object.keys(sampleData.rows[0]).join(", "));
        sampleData.rows.forEach((row, index) => {
          console.log(`Row ${index + 1}:`, JSON.stringify(row, null, 2));
        });
      }

      // Check for NSE/BSE data
      console.log("\n🔍 Checking NSE/BSE data availability...");

      // Count records with different exchange data
      const countQueries = [
        "SELECT COUNT(*) as count FROM company_list WHERE symbol IS NOT NULL",
        "SELECT COUNT(*) as count FROM company_list WHERE nse_security_id IS NOT NULL",
        "SELECT COUNT(*) as count FROM company_list WHERE bse_security_id IS NOT NULL",
        "SELECT COUNT(*) as count FROM company_list WHERE sector_name IS NOT NULL",
      ];

      for (const query of countQueries) {
        try {
          const result = await pool.query(query);
          const columnName = query.match(/WHERE (\w+)/)?.[1] || "unknown";
          console.log(`  - ${columnName}: ${result.rows[0].count} records`);
        } catch (error) {
          const columnName = query.match(/WHERE (\w+)/)?.[1] || "unknown";
          console.log(`  - ${columnName}: Column does not exist`);
        }
      }
    }
  } catch (error) {
    console.error("❌ Database error:", error);
  } finally {
    await pool.end();
  }
}

// Run the check
checkDatabaseSchema().catch(console.error);
