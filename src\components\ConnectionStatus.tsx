import React from "react";

interface ConnectionStatusProps {
  isConnected: boolean;
  messageCount?: number;
  realDataOnly?: boolean;
}

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  isConnected,
  messageCount = 0,
  realDataOnly = true
}) => {
  return (
    <div className="flex items-center space-x-3">
      <div className="flex items-center">
        <div
          className={`w-3 h-3 rounded-full mr-2 ${
            isConnected ? "bg-green-500 animate-pulse" : "bg-red-500"
          }`}
        />
        <span className="text-sm font-medium text-gray-700">
          {isConnected ? "🟢 LIVE DATA" : "🔴 DISCONNECTED"}
        </span>
      </div>

      {realDataOnly && (
        <div className="flex items-center">
          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full font-medium">
            REAL DATA ONLY
          </span>
        </div>
      )}

      {isConnected && messageCount > 0 && (
        <div className="flex items-center">
          <span className="text-xs text-gray-500">
            📊 {messageCount.toLocaleString()} messages
          </span>
        </div>
      )}
    </div>
  );
};

export default ConnectionStatus;
