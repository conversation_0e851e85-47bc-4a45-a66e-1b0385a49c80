{"version": 3, "file": "security.js", "sourceRoot": "", "sources": ["../../src/middleware/security.ts"], "names": [], "mappings": ";;;;;;AACA,4EAA2C;AAC3C,oDAA4B;AAC5B,gDAAwB;AACxB,sCAAsC;AACtC,mCAAyC;AAEzC,0BAA0B;AACb,QAAA,WAAW,GAAG,IAAA,4BAAS,EAAC;IACnC,QAAQ,EAAE,kBAAS,CAAC,QAAQ,CAAC,iBAAiB;IAC9C,GAAG,EAAE,kBAAS,CAAC,QAAQ,CAAC,oBAAoB;IAC5C,OAAO,EAAE,wDAAwD;IACjE,OAAO,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;QACvC,MAAM,IAAA,sBAAc,EAAC,qBAAqB,CAAC,CAAC;IAC9C,CAAC;CACF,CAAC,CAAC;AAEH,qBAAqB;AACR,QAAA,WAAW,GAAG,IAAA,cAAI,EAAC;IAC9B,MAAM,EAAE,kBAAS,CAAC,QAAQ,CAAC,cAAc;IACzC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,CAAC;IACpD,cAAc,EAAE,CAAC,cAAc,EAAE,eAAe,CAAC;IACjD,WAAW,EAAE,IAAI;IACjB,MAAM,EAAE,KAAK,EAAE,WAAW;CAC3B,CAAC,CAAC;AAEH,8BAA8B;AACjB,QAAA,eAAe,GAAG,IAAA,gBAAM,EAAC;IACpC,qBAAqB,EAAE;QACrB,UAAU,EAAE;YACV,UAAU,EAAE,CAAC,QAAQ,CAAC;YACtB,SAAS,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC;YACxC,QAAQ,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC;YACvC,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;YACrC,UAAU,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;YACxC,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC;YACtC,SAAS,EAAE,CAAC,QAAQ,CAAC;YACrB,QAAQ,EAAE,CAAC,QAAQ,CAAC;YACpB,QAAQ,EAAE,CAAC,QAAQ,CAAC;SACrB;KACF;IACD,yBAAyB,EAAE,IAAI;IAC/B,uBAAuB,EAAE,IAAI;IAC7B,yBAAyB,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE;IAClD,kBAAkB,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;IACpC,UAAU,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;IAC9B,aAAa,EAAE,IAAI;IACnB,IAAI,EAAE;QACJ,MAAM,EAAE,QAAQ;QAChB,iBAAiB,EAAE,IAAI;QACvB,OAAO,EAAE,IAAI;KACd;IACD,QAAQ,EAAE,IAAI;IACd,OAAO,EAAE,IAAI;IACb,kBAAkB,EAAE,IAAI;IACxB,4BAA4B,EAAE,EAAE,iBAAiB,EAAE,MAAM,EAAE;IAC3D,cAAc,EAAE,EAAE,MAAM,EAAE,iCAAiC,EAAE;IAC7D,SAAS,EAAE,IAAI;CAChB,CAAC,CAAC;AAEH,gCAAgC;AACzB,MAAM,eAAe,GAAG,CAAC,MAAW,EAAE,EAAE;IAC7C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,IAAI,CAAC;YACH,MAAM,CAAC,KAAK,CAAC;gBACX,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,KAAK,EAAE,GAAG,CAAC,KAAK;gBAChB,MAAM,EAAE,GAAG,CAAC,MAAM;aACnB,CAAC,CAAC;YACH,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,CAAC;QACd,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AAbW,QAAA,eAAe,mBAa1B;AAEF,oCAAoC;AAC7B,MAAM,kBAAkB,GAAG,CAChC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,OAAO,IAAI,EAAE,CAAC;IAChB,CAAC;IAED,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;IACtB,MAAM,OAAO,GAAG,kBAAS,CAAC,MAAM,CAAC,WAAW,CAAC;IAC7C,MAAM,iBAAiB,GAAG,kBAAS,CAAC,MAAM,CAAC,iBAAiB,CAAC;IAE7D,kBAAkB;IAClB,IAAI,IAAI,CAAC,IAAI,GAAG,OAAO,EAAE,CAAC;QACxB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,gBAAgB;YACvB,OAAO,EAAE,+BAA+B,OAAO,GAAG,IAAI,GAAG,IAAI,IAAI;SAClE,CAAC,CAAC;IACL,CAAC;IAED,uBAAuB;IACvB,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,WAAW,EAAE,CAAC;IAC9D,IAAI,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,CAAC;QACnD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,mBAAmB;YAC1B,OAAO,EAAE,QAAQ,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB;SAClE,CAAC,CAAC;IACL,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAjCW,QAAA,kBAAkB,sBAiC7B;AAEF,gCAAgC;AACzB,MAAM,cAAc,GAAG,CAC5B,GAAY,EACZ,GAAa,EACb,IAAkB,EACZ,EAAE;IACR,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;IAExC,IAAI,CAAC,MAAM,IAAI,MAAM,KAAK,kBAAS,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;QACpD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,iBAAiB;YACxB,OAAO,EAAE,gCAAgC;SAC1C,CAAC,CAAC;QACH,OAAO;IACT,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAjBW,QAAA,cAAc,kBAiBzB;AAEF,kCAAkC;AAC3B,MAAM,eAAe,GAAG,CAC7B,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,4BAA4B;IAC5B,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC;QACd,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YACrC,IAAI,OAAO,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC;gBACvC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAW,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,wBAAwB;IACxB,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;QACb,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YACpC,IAAI,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC;gBACtC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAChD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAxBW,QAAA,eAAe,mBAwB1B;AAEF,6BAA6B;AAC7B,SAAS,cAAc,CAAC,GAAW;IACjC,OAAO,GAAG;SACP,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,iBAAiB;SACtC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,8BAA8B;SAC3D,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,wBAAwB;SAChD,IAAI,EAAE,CAAC;AACZ,CAAC;AAED,yCAAyC;AAClC,MAAM,oBAAoB,GAAG,CAClC,GAAU,EACV,GAAY,EACZ,GAAa,EACb,IAAkB,EACZ,EAAE;IACR,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QACrC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,qBAAqB;YAC5B,OAAO,EAAE,2CAA2C;SACrD,CAAC,CAAC;QACH,OAAO;IACT,CAAC;IAED,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;QAC7B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,YAAY;YACnB,OAAO,EAAE,kCAAkC;SAC5C,CAAC,CAAC;QACH,OAAO;IACT,CAAC;IAED,IAAI,CAAC,GAAG,CAAC,CAAC;AACZ,CAAC,CAAC;AAzBW,QAAA,oBAAoB,wBAyB/B"}