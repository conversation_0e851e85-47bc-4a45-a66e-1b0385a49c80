"use client";

import React, { useState, useEffect } from "react";
import { useParams, useSearchParams, useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  TrendingUp,
  TrendingDown,
  ArrowLeft,
  Search,
  Package,
  BarChart3,
  Activity,
  AlertCircle,
  RefreshCw,
} from "lucide-react";
import LoadingSpinner from "@/components/LoadingSpinner";

interface StockData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
  companyName?: string;
  sector?: string;
  industry?: string;
  subSector?: string;
  microCategory?: string;
}

interface CompanyInfo {
  company_name: string;
  nse_symbol?: string;
  bse_symbol?: string;
  nse_security_id?: string;
  bse_security_id?: string;
  sector_name?: string;
  industry_name?: string;
  sub_sector?: string;
  micro_category?: string;
}

export default function MicroCategoryDetailPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();

  const microCategoryName = decodeURIComponent(
    params["micro-category"] as string
  );
  const exchange = searchParams.get("exchange") || "NSE";

  const [stocks, setStocks] = useState<StockData[]>([]);
  const [filteredStocks, setFilteredStocks] = useState<StockData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<"change" | "volume" | "price">("change");
  const [companies, setCompanies] = useState<{ [key: string]: CompanyInfo }>(
    {}
  );

  // FAST SIMPLE PATTERN - Same as simple dashboard
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // FAST: Parallel fetch with small limits
        const [companiesRes, marketRes] = await Promise.all([
          fetch("/api/companies?limit=500"),
          fetch("/api/data?exchange=NSE_EQ&limit=500"),
        ]);

        // FAST: Simple company mapping
        let companyMap: { [key: string]: any } = {};
        if (companiesRes.ok) {
          const companiesData = await companiesRes.json();
          if (companiesData.success) {
            companiesData.data.forEach((company: any) => {
              if (company.nse_security_id)
                companyMap[company.nse_security_id] = company;
              if (company.bse_security_id)
                companyMap[company.bse_security_id] = company;
              if (company.symbol) companyMap[company.symbol] = company;
            });
          }
        }

        // FAST: Simple market data processing
        if (marketRes.ok) {
          const marketData = await marketRes.json();
          if (marketData.latestData) {
            const microCategoryStocks = marketData.latestData
              .filter((stock: any) => stock && stock.ticker && stock.ltp)
              .map((stock: any) => {
                const company =
                  companyMap[stock.securityId] || companyMap[stock.ticker];
                return {
                  ticker: stock.ticker,
                  securityId: stock.securityId,
                  ltp: stock.ltp || 0,
                  change: stock.change || 0,
                  changePercent: stock.changePercent || 0,
                  volume: stock.volume || 0,
                  high: stock.high || 0,
                  low: stock.low || 0,
                  open: stock.open || 0,
                  close: stock.close || 0,
                  companyName: company?.company_name || stock.ticker,
                  sector: company?.sector_name || "Unknown",
                  industry: company?.industry_name || "Unknown",
                  subSector: company?.sub_sector || "Unknown",
                  microCategory: company?.micro_category || "Unknown",
                };
              })
              .filter(
                (stock: any) => stock.microCategory === microCategoryName
              );

            setStocks(microCategoryStocks);
            setFilteredStocks(microCategoryStocks);
          }
        }
      } catch (error) {
        console.error("Error:", error);
        setError(
          error instanceof Error ? error.message : "Failed to load data"
        );
        setStocks([]);
        setFilteredStocks([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [microCategoryName, exchange]);

  // Filter and sort stocks
  useEffect(() => {
    const filtered = stocks.filter(
      (stock) =>
        stock.ticker.toLowerCase().includes(searchTerm.toLowerCase()) ||
        stock.companyName?.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Sort stocks
    const sorted = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case "change":
          return Math.abs(b.changePercent) - Math.abs(a.changePercent);
        case "volume":
          return b.volume - a.volume;
        case "price":
          return b.ltp - a.ltp;
        default:
          return 0;
      }
    });

    setFilteredStocks(sorted);
  }, [stocks, searchTerm, sortBy]);

  const formatPrice = (price: number | undefined | null) => {
    if (price == null || isNaN(price)) return "₹0.00";
    return `₹${price.toFixed(2)}`;
  };

  const formatChange = (
    change: number | undefined | null,
    changePercent: number | undefined | null
  ) => {
    if (
      change == null ||
      changePercent == null ||
      isNaN(change) ||
      isNaN(changePercent)
    ) {
      return "0.00 (0.00%)";
    }
    const sign = change >= 0 ? "+" : "";
    return `${sign}${change.toFixed(2)} (${sign}${changePercent.toFixed(2)}%)`;
  };

  const formatVolume = (volume: number | undefined | null) => {
    if (volume == null || isNaN(volume)) return "0";
    if (volume >= 10000000) return `${(volume / 10000000).toFixed(1)}Cr`;
    if (volume >= 100000) return `${(volume / 100000).toFixed(1)}L`;
    if (volume >= 1000) return `${(volume / 1000).toFixed(1)}K`;
    return volume.toString();
  };

  const gainers = filteredStocks.filter((s) => s.change > 0).length;
  const losers = filteredStocks.filter((s) => s.change < 0).length;
  const avgChange =
    filteredStocks.length > 0
      ? filteredStocks.reduce((sum, s) => sum + s.changePercent, 0) /
        filteredStocks.length
      : 0;

  const retryFetch = () => {
    setError(null);
    setLoading(true);
    // Trigger re-fetch by updating a dependency
    window.location.reload();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20 flex items-center justify-center">
        <LoadingSpinner
          message={`Loading ${microCategoryName} micro-category data...`}
          size="lg"
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20 flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <CardContent className="p-8 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Failed to Load Data
            </h3>
            <p className="text-gray-600 mb-6">{error}</p>
            <Button onClick={retryFetch} className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5 pointer-events-none" />

      <div className="relative z-10 space-y-8 p-4 sm:p-6 lg:p-8">
        {/* Enhanced Professional Header */}
        <div className="page-header rounded-2xl p-8 shadow-large">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="flex items-center gap-6">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.back()}
                className="flex items-center gap-2 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 transition-all"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Micro-Categories
              </Button>

              <div className="flex items-center gap-4">
                <div className="flex items-center justify-center w-16 h-16 bg-gradient-trading rounded-2xl shadow-glow">
                  <Package className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="header-title text-4xl font-bold mb-2">
                    {microCategoryName}
                  </h1>
                  <div className="flex items-center gap-4">
                    <p className="text-gray-600 text-lg">{exchange} Exchange</p>
                    <div className="status-indicator live">
                      <span>Live Data</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card variant="elevated" className="trading-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">
                    Total Stocks
                  </p>
                  <p className="text-3xl font-bold text-gray-900 price-ticker">
                    {filteredStocks.length}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    In {microCategoryName}
                  </p>
                </div>
                <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-xl">
                  <Package className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card variant="elevated" className="trading-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">
                    Average Change
                  </p>
                  <p
                    className={`text-3xl font-bold price-ticker ${
                      avgChange >= 0 ? "text-green-600" : "text-red-600"
                    }`}
                  >
                    {avgChange >= 0 ? "+" : ""}
                    {avgChange.toFixed(2)}%
                  </p>
                  <p
                    className={`text-xs mt-1 ${
                      avgChange >= 0 ? "text-green-600" : "text-red-600"
                    }`}
                  >
                    {avgChange >= 0 ? "Bullish trend" : "Bearish trend"}
                  </p>
                </div>
                <div
                  className={`flex items-center justify-center w-12 h-12 rounded-xl ${
                    avgChange >= 0 ? "bg-green-100" : "bg-red-100"
                  }`}
                >
                  <BarChart3
                    className={`h-6 w-6 ${
                      avgChange >= 0 ? "text-green-600" : "text-red-600"
                    }`}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card variant="elevated" className="trading-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">
                    Gainers
                  </p>
                  <p className="text-3xl font-bold text-green-600 price-ticker">
                    {gainers}
                  </p>
                  <p className="text-xs text-green-600 mt-1">
                    {((gainers / filteredStocks.length) * 100).toFixed(1)}% of
                    stocks
                  </p>
                </div>
                <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-xl">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card variant="elevated" className="trading-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">
                    Losers
                  </p>
                  <p className="text-3xl font-bold text-red-600 price-ticker">
                    {losers}
                  </p>
                  <p className="text-xs text-red-600 mt-1">
                    {((losers / filteredStocks.length) * 100).toFixed(1)}% of
                    stocks
                  </p>
                </div>
                <div className="flex items-center justify-center w-12 h-12 bg-red-100 rounded-xl">
                  <TrendingDown className="h-6 w-6 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Filters and Search */}
        <Card variant="elevated" className="p-6">
          <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
            <div className="flex items-center gap-4 flex-1">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search stocks by name or ticker..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="search-input pl-10"
                />
              </div>
              <div className="text-sm text-gray-600">
                {filteredStocks.length} stocks found
              </div>
            </div>

            <div className="flex items-center gap-3">
              <span className="text-sm font-medium text-gray-700">
                Sort by:
              </span>
              <div className="flex gap-2">
                <Button
                  variant={sortBy === "change" ? "gradient" : "outline"}
                  size="sm"
                  onClick={() => setSortBy("change")}
                  className="font-semibold"
                >
                  Change %
                </Button>
                <Button
                  variant={sortBy === "volume" ? "gradient" : "outline"}
                  size="sm"
                  onClick={() => setSortBy("volume")}
                  className="font-semibold"
                >
                  Volume
                </Button>
                <Button
                  variant={sortBy === "price" ? "gradient" : "outline"}
                  size="sm"
                  onClick={() => setSortBy("price")}
                  className="font-semibold"
                >
                  Price
                </Button>
              </div>
            </div>
          </div>
        </Card>

        {/* Enhanced Stocks List */}
        <Card variant="elevated" className="overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <CardTitle className="text-xl font-bold text-gray-900">
                {microCategoryName} Stocks
              </CardTitle>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Activity className="w-4 h-4" />
                <span>Live Market Data</span>
              </div>
            </div>
          </CardHeader>

          <CardContent className="p-0">
            <div className="divide-y divide-gray-100">
              {filteredStocks.map((stock, index) => (
                <div
                  key={stock.securityId}
                  className="stock-row group cursor-pointer p-6 hover:bg-blue-50/50 transition-all duration-200"
                  onClick={() => router.push(`/stock/${stock.ticker}`)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4 flex-1">
                      <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-xl text-sm font-bold text-blue-600 group-hover:bg-blue-200 transition-colors">
                        {index + 1}
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-3 mb-1">
                          <h3 className="font-bold text-lg text-gray-900 group-hover:text-blue-600 transition-colors">
                            {stock.ticker}
                          </h3>
                          {stock.sector && stock.sector !== "Unknown" && (
                            <Badge
                              variant="outline"
                              size="sm"
                              className="text-xs"
                            >
                              {stock.sector}
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 truncate max-w-md group-hover:text-gray-700 transition-colors">
                          {stock.companyName}
                        </p>
                        <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                          <span>Vol: {formatVolume(stock.volume)}</span>
                          <span>High: {formatPrice(stock.high)}</span>
                          <span>Low: {formatPrice(stock.low)}</span>
                        </div>
                      </div>
                    </div>

                    <div className="text-right space-y-1">
                      <div className="text-2xl font-bold price-ticker text-gray-900">
                        {formatPrice(stock.ltp)}
                      </div>
                      <div
                        className={`text-sm font-bold px-3 py-1 rounded-full ${
                          stock.change >= 0
                            ? "text-green-700 bg-green-100"
                            : "text-red-700 bg-red-100"
                        }`}
                      >
                        {formatChange(stock.change, stock.changePercent)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {stock.change >= 0 ? "↗" : "↘"}{" "}
                        {Math.abs(stock.change).toFixed(2)}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {filteredStocks.length === 0 && !loading && (
          <Card variant="elevated" className="text-center py-16">
            <CardContent>
              <div className="flex flex-col items-center gap-4">
                <div className="flex items-center justify-center w-16 h-16 bg-gray-100 rounded-2xl">
                  <Search className="w-8 h-8 text-gray-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    No stocks found
                  </h3>
                  <p className="text-gray-600">
                    No stocks found in {microCategoryName} micro-category
                    matching your search criteria.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
