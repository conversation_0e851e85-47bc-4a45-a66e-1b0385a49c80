"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  Building2,
  Activity,
} from "lucide-react";
import { useRouter } from "next/navigation";
import SectorAnalytics from "./SectorAnalytics";

interface StockData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
  companyName?: string;
  sector?: string;
  industry?: string;
}

interface SectorData {
  sector: string;
  stocks: StockData[];
  totalStocks: number;
  gainers: number;
  losers: number;
  avgChange: number;
  totalVolume: number;
}

interface CompanyInfo {
  company_name: string;
  symbol: string; // The actual field from API
  nse_security_id?: string;
  bse_security_id?: string;
  sector_name?: string;
  industry_new_name?: string; // Fixed: Use correct database field name
}

export default function SectorDashboard() {
  const router = useRouter();
  const [selectedExchange, setSelectedExchange] = useState<"NSE" | "BSE">(
    "NSE"
  );
  const [sectorData, setSectorData] = useState<SectorData[]>([]);
  const [loading, setLoading] = useState(true);
  const [companies, setCompanies] = useState<{ [key: string]: CompanyInfo }>(
    {}
  );
  const [activeTab, setActiveTab] = useState<"overview" | "analytics">(
    "overview"
  );
  const [totalDatabaseStocks, setTotalDatabaseStocks] = useState({
    nse: 0,
    bse: 0,
    total: 0,
  });

  // Fetch company information and database stats
  useEffect(() => {
    const fetchCompanies = async () => {
      try {
        console.log("🔄 Fetching companies data...");
        const response = await fetch("/api/companies?limit=10000");
        const data = await response.json();

        console.log("📊 Companies API response:", {
          success: data.success,
          count: data.data?.length,
        });

        if (data.success) {
          const companyMap: { [key: string]: CompanyInfo } = {};
          let nseCount = 0;
          let bseCount = 0;

          data.data.forEach((company: CompanyInfo) => {
            // Map by symbol (this is the ticker used in market data)
            if (company.symbol) {
              companyMap[company.symbol] = company;
            }

            // Count NSE and BSE listings
            if (company.nse_security_id) nseCount++;
            if (company.bse_security_id) bseCount++;
          });

          console.log("✅ Companies mapped:", Object.keys(companyMap).length);
          console.log("📊 Database stats:", {
            nseCount,
            bseCount,
            total: nseCount + bseCount,
          });

          setCompanies(companyMap);
          setTotalDatabaseStocks({
            nse: nseCount,
            bse: bseCount,
            total: nseCount + bseCount,
          });
        }
      } catch (error) {
        console.error("❌ Error fetching companies:", error);
      }
    };

    fetchCompanies();
  }, []);

  // Fetch market data and organize by sectors
  useEffect(() => {
    const fetchMarketData = async () => {
      try {
        console.log("🔄 Fetching market data for sectors...", {
          exchange: selectedExchange,
          companiesCount: Object.keys(companies).length,
        });
        setLoading(true);
        const response = await fetch(
          `/api/all-instruments?exchange=${selectedExchange}_EQ`
        );
        const data = await response.json();

        console.log("📊 All instruments response:", {
          hasInstruments: !!data.instruments,
          count: data.instruments?.length,
          totalSubscribed: data.totalInstruments,
          liveInstruments: data.liveInstruments,
        });

        if (data.instruments) {
          const sectorMap: { [key: string]: StockData[] } = {};

          data.instruments.forEach((stock: StockData) => {
            // Get company info for this stock
            const companyInfo = companies[stock.ticker];

            // Very lenient filtering - include all stocks
            let sector = "Others";
            let companyName = stock.ticker;
            let industry = "Unknown";

            if (companyInfo && companyInfo.sector_name) {
              sector = companyInfo.sector_name;
              companyName = companyInfo.company_name || stock.ticker;
              industry = companyInfo.industry_new_name || "Unknown"; // Fixed: Use correct field name
            }

            if (!sectorMap[sector]) {
              sectorMap[sector] = [];
            }

            // Add company info to stock data
            const enrichedStock = {
              ...stock,
              companyName,
              sector,
              industry,
            };

            sectorMap[sector].push(enrichedStock);
          });

          // Convert to sector data with statistics
          const sectors: SectorData[] = Object.entries(sectorMap).map(
            ([sector, stocks]) => {
              const gainers = stocks.filter((s) => s.change > 0).length;
              const losers = stocks.filter((s) => s.change < 0).length;
              const avgChange =
                stocks.reduce((sum, s) => sum + s.changePercent, 0) /
                stocks.length;
              const totalVolume = stocks.reduce((sum, s) => sum + s.volume, 0);

              return {
                sector,
                stocks: stocks.sort(
                  (a, b) =>
                    Math.abs(b.changePercent) - Math.abs(a.changePercent)
                ), // Sort by highest change
                totalStocks: stocks.length,
                gainers,
                losers,
                avgChange,
                totalVolume,
              };
            }
          );

          // Sort sectors by average change
          sectors.sort((a, b) => Math.abs(b.avgChange) - Math.abs(a.avgChange));
          console.log(
            "✅ Sectors processed:",
            sectors.length,
            "sectors with",
            sectors.reduce((sum, s) => sum + s.totalStocks, 0),
            "total stocks"
          );
          setSectorData(sectors);
        }
      } catch (error) {
        console.error("❌ Error fetching market data:", error);
      } finally {
        setLoading(false);
      }
    };

    console.log("🔍 Fetching market data regardless of companies count:", {
      companiesCount: Object.keys(companies).length,
      exchange: selectedExchange,
    });

    // Always fetch market data - companies data will be used if available
    fetchMarketData();
  }, [selectedExchange, companies]);

  const formatPrice = (price: number) => {
    return `₹${price.toFixed(2)}`;
  };

  const formatChange = (change: number, changePercent: number) => {
    const sign = change >= 0 ? "+" : "";
    return `${sign}${change.toFixed(2)} (${sign}${changePercent.toFixed(2)}%)`;
  };

  // const formatVolume = (volume: number) => { // Commented out as it's not used
  //   if (volume >= 10000000) return `${(volume / 10000000).toFixed(1)}Cr`;
  //   if (volume >= 100000) return `${(volume / 100000).toFixed(1)}L`;
  //   if (volume >= 1000) return `${(volume / 1000).toFixed(1)}K`;
  //   return volume.toString();
  // };

  const handleViewAllStocks = (sectorName: string) => {
    // Navigate to a sector-specific page with all stocks
    const encodedSector = encodeURIComponent(sectorName);
    router.push(`/sectors/${encodedSector}?exchange=${selectedExchange}`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading sector data...</div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Enhanced Professional Header */}
      <div className="page-header rounded-2xl p-8 shadow-large">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <div className="flex items-center gap-6">
            <div className="flex items-center justify-center w-16 h-16 bg-gradient-trading rounded-2xl shadow-glow">
              <Building2 className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="header-title text-4xl font-bold mb-2">
                Sector Dashboard
              </h1>
              <p className="text-gray-600 text-lg">
                Professional sector analysis and market intelligence
              </p>
              <div className="flex items-center gap-4 mt-3">
                <div className="status-indicator live">
                  <span>Live Market Data</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <Activity className="w-4 h-4" />
                  <span>Real-time Updates</span>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Exchange Toggle */}
          <div className="flex flex-col gap-4">
            <div className="flex items-center gap-3 bg-white/80 backdrop-blur-sm rounded-xl p-2 border border-gray-200 shadow-soft">
              <Button
                variant={selectedExchange === "NSE" ? "gradient" : "ghost"}
                size="sm"
                onClick={() => setSelectedExchange("NSE")}
                className="min-w-[80px] font-semibold"
              >
                NSE
              </Button>
              <Button
                variant={selectedExchange === "BSE" ? "gradient" : "ghost"}
                size="sm"
                onClick={() => setSelectedExchange("BSE")}
                className="min-w-[80px] font-semibold"
              >
                BSE
              </Button>
            </div>
            <div className="text-center">
              <p className="text-xs text-gray-500">Exchange</p>
              <p className="text-sm font-semibold text-gray-700">
                {selectedExchange} Market
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Tab Navigation */}
      <div className="bg-white rounded-2xl shadow-medium border border-gray-200 overflow-hidden">
        <nav className="flex">
          <button
            type="button"
            onClick={() => setActiveTab("overview")}
            className={`flex-1 py-4 px-6 font-semibold text-sm transition-all duration-200 ${
              activeTab === "overview"
                ? "bg-gradient-trading text-white shadow-lg"
                : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
            }`}
          >
            <div className="flex items-center justify-center gap-2">
              <BarChart3 className="w-4 h-4" />
              <span>Market Overview</span>
            </div>
          </button>
          <button
            type="button"
            onClick={() => setActiveTab("analytics")}
            className={`flex-1 py-4 px-6 font-semibold text-sm transition-all duration-200 ${
              activeTab === "analytics"
                ? "bg-gradient-trading text-white shadow-lg"
                : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
            }`}
          >
            <div className="flex items-center justify-center gap-2">
              <Activity className="w-4 h-4" />
              <span>Advanced Analytics</span>
            </div>
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === "overview" && (
        <>
          {/* Market Closed Notice */}
          {selectedExchange === "BSE" && sectorData.length === 0 && (
            <Card className="border-orange-200 bg-orange-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 rounded-full bg-orange-500 animate-pulse"></div>
                  <div>
                    <p className="font-medium text-orange-800">
                      BSE Market Closed
                    </p>
                    <p className="text-sm text-orange-600">
                      BSE trading hours: 9:15 AM - 3:30 PM IST. Real-time data
                      will be available during market hours.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Enhanced Sector Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
            <Card variant="elevated" className="trading-card">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">
                      Total Sectors
                    </p>
                    <p className="text-3xl font-bold text-gray-900 price-ticker">
                      {sectorData.length}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">Active markets</p>
                  </div>
                  <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-xl">
                    <BarChart3 className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card variant="elevated" className="trading-card">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">
                      Total Stocks
                    </p>
                    <div className="flex items-baseline gap-2">
                      <p className="text-3xl font-bold text-gray-900 price-ticker">
                        {sectorData.reduce(
                          (sum, sector) => sum + sector.totalStocks,
                          0
                        )}
                      </p>
                      <p className="text-sm text-green-600 font-semibold">
                        / {selectedExchange === "NSE" ? "2103" : "4323"}
                      </p>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      All Subscribed Instruments ({selectedExchange})
                    </p>
                  </div>
                  <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-xl">
                    <Building2 className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card variant="elevated" className="trading-card">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">
                      Gainers
                    </p>
                    <p className="text-3xl font-bold text-green-600 price-ticker">
                      {sectorData.reduce(
                        (sum, sector) => sum + sector.gainers,
                        0
                      )}
                    </p>
                    <p className="text-xs text-green-600 mt-1">
                      Bullish stocks
                    </p>
                  </div>
                  <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-xl">
                    <TrendingUp className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card variant="elevated" className="trading-card">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">
                      Losers
                    </p>
                    <p className="text-3xl font-bold text-red-600 price-ticker">
                      {sectorData.reduce(
                        (sum, sector) => sum + sector.losers,
                        0
                      )}
                    </p>
                    <p className="text-xs text-red-600 mt-1">Bearish stocks</p>
                  </div>
                  <div className="flex items-center justify-center w-12 h-12 bg-red-100 rounded-xl">
                    <TrendingDown className="h-6 w-6 text-red-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card variant="elevated" className="trading-card">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">
                      Live Trading
                    </p>
                    <div className="flex items-baseline gap-2">
                      <p className="text-2xl font-bold text-blue-600 price-ticker">
                        {sectorData.reduce(
                          (sum, sector) => sum + sector.totalStocks,
                          0
                        )}
                      </p>
                      <p className="text-sm text-gray-500">
                        / {selectedExchange === "NSE" ? "2103" : "4323"}
                      </p>
                    </div>
                    <p className="text-xs text-blue-600 mt-1">
                      {((sectorData.reduce((sum, sector) => sum + sector.totalStocks, 0) /
                        (selectedExchange === "NSE" ? 2103 : 4323)) * 100).toFixed(1)}% active
                    </p>
                  </div>
                  <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-xl">
                    <Activity className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Enhanced Sector Cards */}
          <div className="sector-grid">
            {sectorData.map((sector) => (
              <Card
                key={sector.sector}
                variant="elevated"
                className="trading-card group cursor-pointer"
                onClick={() => handleViewAllStocks(sector.sector)}
              >
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between mb-3">
                    <CardTitle className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                      {sector.sector}
                    </CardTitle>
                    <Badge
                      variant={
                        sector.avgChange >= 0 ? "success" : "destructive"
                      }
                      size="lg"
                      className="font-semibold"
                    >
                      {sector.avgChange >= 0 ? "+" : ""}
                      {sector.avgChange.toFixed(2)}%
                    </Badge>
                  </div>

                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div className="bg-gray-50 rounded-lg p-3">
                      <p className="text-lg font-bold text-gray-900">
                        {sector.totalStocks}
                      </p>
                      <p className="text-xs text-gray-600">Total Stocks</p>
                    </div>
                    <div className="bg-green-50 rounded-lg p-3">
                      <p className="text-lg font-bold text-green-600">
                        {sector.gainers}
                      </p>
                      <p className="text-xs text-green-600">Gainers</p>
                    </div>
                    <div className="bg-red-50 rounded-lg p-3">
                      <p className="text-lg font-bold text-red-600">
                        {sector.losers}
                      </p>
                      <p className="text-xs text-red-600">Losers</p>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-semibold text-gray-700">
                      Top Performers
                    </h4>
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      <Activity className="w-3 h-3" />
                      <span>Live</span>
                    </div>
                  </div>

                  {/* Top 3 stocks in sector */}
                  {sector.stocks.slice(0, 3).map((stock, index) => (
                    <div
                      key={stock.securityId}
                      className="stock-row group/stock"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3 flex-1">
                          <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-lg text-xs font-bold text-blue-600">
                            {index + 1}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="font-semibold text-sm text-gray-900 group-hover/stock:text-blue-600 transition-colors">
                              {stock.ticker}
                            </div>
                            <div className="text-xs text-gray-500 truncate">
                              {stock.companyName}
                            </div>
                          </div>
                        </div>

                        <div className="text-right">
                          <div className="font-bold text-sm price-ticker">
                            {formatPrice(stock.ltp)}
                          </div>
                          <div
                            className={`text-xs font-semibold ${
                              stock.change >= 0
                                ? "text-green-600"
                                : "text-red-600"
                            }`}
                          >
                            {formatChange(stock.change, stock.changePercent)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}

                  {sector.stocks.length > 3 && (
                    <div className="pt-4 border-t border-gray-100">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full font-semibold group-hover:bg-blue-50 group-hover:border-blue-300 group-hover:text-blue-600 transition-all"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleViewAllStocks(sector.sector);
                        }}
                      >
                        View all {sector.stocks.length} stocks
                        <svg
                          className="w-4 h-4 ml-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 5l7 7-7 7"
                          />
                        </svg>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </>
      )}

      {/* Analytics Tab */}
      {activeTab === "analytics" && (
        <SectorAnalytics
          sectorData={sectorData}
          selectedExchange={selectedExchange}
        />
      )}
    </div>
  );
}
