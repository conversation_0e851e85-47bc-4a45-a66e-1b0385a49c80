import { Request, Response, NextFunction } from "express";
import cors from "cors";
export declare const rateLimiter: import("express-rate-limit").RateLimitRequestHandler;
export declare const corsOptions: (req: cors.CorsRequest, res: {
    statusCode?: number | undefined;
    setHeader(key: string, value: string): any;
    end(): any;
}, next: (err?: any) => any) => void;
export declare const securityHeaders: (req: import("http").IncomingMessage, res: import("http").ServerResponse, next: (err?: unknown) => void) => void;
export declare const validateRequest: (schema: any) => (req: Request, res: Response, next: NextFunction) => void;
export declare const validateFileUpload: (req: Request, res: Response, next: NextFunction) => void | Response<any, Record<string, any>>;
export declare const validateApiKey: (req: Request, res: Response, next: NextFunction) => void;
export declare const sanitizeRequest: (req: Request, res: Response, next: NextFunction) => void;
export declare const securityErrorHandler: (err: Error, req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=security.d.ts.map