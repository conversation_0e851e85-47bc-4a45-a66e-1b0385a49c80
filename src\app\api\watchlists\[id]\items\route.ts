import { NextResponse } from "next/server";
import { DatabaseService } from "@/services/database";

const db = DatabaseService.getInstance();

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const watchlistId = parseInt(resolvedParams.id);

    if (isNaN(watchlistId)) {
      return new Response(
        JSON.stringify({ success: false, error: "Invalid watchlist ID" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    // Get watchlist info to determine type
    const watchlists = await db.getWatchlists();
    const watchlist = watchlists.find((w) => w.id === watchlistId);

    if (!watchlist) {
      return new Response(
        JSON.stringify({ success: false, error: "Watchlist not found" }),
        { status: 404, headers: { "Content-Type": "application/json" } }
      );
    }

    let items: string[] = [];

    // Get items based on watchlist type
    switch (watchlist.type) {
      case "sector":
        items = await db.getSectorWatchlist(watchlistId);
        break;
      case "industry":
        items = await db.getIndustryWatchlist(watchlistId);
        break;
      case "sub-sector":
        items = await db.getSubSectorWatchlist(watchlistId);
        break;
      case "micro-category":
        items = await db.getMicroCategoryWatchlist(watchlistId);
        break;
      default:
        return new Response(
          JSON.stringify({ success: false, error: "Unknown watchlist type" }),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
    }

    return NextResponse.json({
      success: true,
      data: {
        watchlist,
        items,
      },
    });
  } catch (error) {
    console.error("Error fetching watchlist items:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Failed to fetch watchlist items",
      }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
}

export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const watchlistId = parseInt(resolvedParams.id);
    const { item } = await request.json();

    if (isNaN(watchlistId) || !item) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Watchlist ID and item are required",
        }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    // Get watchlist info to determine type
    const watchlists = await db.getWatchlists();
    const watchlist = watchlists.find((w) => w.id === watchlistId);

    if (!watchlist) {
      return new Response(
        JSON.stringify({ success: false, error: "Watchlist not found" }),
        { status: 404, headers: { "Content-Type": "application/json" } }
      );
    }

    // Add item based on watchlist type
    switch (watchlist.type) {
      case "sector":
        await db.addToSectorWatchlist(watchlistId, item);
        break;
      case "industry":
        await db.addToIndustryWatchlist(watchlistId, item);
        break;
      case "sub-sector":
        await db.addToSubSectorWatchlist(watchlistId, item);
        break;
      case "micro-category":
        await db.addToMicroCategoryWatchlist(watchlistId, item);
        break;
      default:
        return new Response(
          JSON.stringify({ success: false, error: "Unknown watchlist type" }),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
    }

    return NextResponse.json({
      success: true,
      message: "Item added to watchlist",
    });
  } catch (error) {
    console.error("Error adding item to watchlist:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Failed to add item to watchlist",
      }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
}
