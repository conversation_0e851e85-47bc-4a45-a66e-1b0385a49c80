import React, { useEffect, useState, useMemo } from "react";
import { MarketData as MarketDataType } from "../types";

interface MarketDataProps {
  symbol: string;
  data: MarketDataType;
}

const MarketData: React.FC<MarketDataProps> = ({ symbol, data }) => {
  const [priceChange, setPriceChange] = useState<number>(0);
  const [isPositive, setIsPositive] = useState<boolean>(true);

  useEffect(() => {
    if (data.change !== undefined) {
      setPriceChange(data.change);
      setIsPositive(data.change >= 0);
    }
  }, [data.change]);

  const formatNumber = useMemo(() => {
    return (num: number | undefined | null, decimals: number = 2) => {
      if (num == null || isNaN(num)) return "0.00";
      return num.toFixed(decimals);
    };
  }, []);

  const formatVolume = useMemo(() => {
    return (num: number | undefined | null) => {
      if (num == null || isNaN(num)) return "0";
      return num.toLocaleString();
    };
  }, []);

  const formatTime = useMemo(() => {
    return (timestamp: number) => new Date(timestamp).toLocaleTimeString();
  }, []);

  return (
    <div className="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow">
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-lg font-semibold text-gray-800">{symbol}</h3>
        <span className="text-sm text-gray-500">{data.exchange}</span>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="text-sm text-gray-600">Last Price</p>
          <p className="text-xl font-bold">{formatNumber(data.ltp)}</p>
        </div>

        <div>
          <p className="text-sm text-gray-600">Change</p>
          <p
            className={`text-xl font-bold ${
              isPositive ? "text-green-600" : "text-red-600"
            }`}
          >
            {formatNumber(priceChange)} (
            {formatNumber((priceChange / data.ltp) * 100)}%)
          </p>
        </div>

        <div>
          <p className="text-sm text-gray-600">Volume</p>
          <p className="text-lg font-semibold">{formatVolume(data.volume)}</p>
        </div>

        <div>
          <p className="text-sm text-gray-600">High/Low</p>
          <p className="text-lg font-semibold">
            {formatNumber(data.high)} / {formatNumber(data.low)}
          </p>
        </div>
      </div>

      <div className="mt-4 text-xs text-gray-500">
        Last updated: {formatTime(data.timestamp)}
      </div>
    </div>
  );
};

export default React.memo(MarketData);
