"use client";

import React, { useEffect, useState } from "react";
import { createWebSocketConnection } from "@/utils/websocket";
import Link from "next/link";
import StocksColumnLayout from "../../components/StocksColumnLayout";
import ConnectionStatus from "../../components/ConnectionStatus";
import Stats from "../../components/Stats";
import Navigation from "../../components/Navigation";
import KeyIndices from "../../components/KeyIndices";

interface MarketData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
}

interface Stats {
  instrumentCount: number;
  updateRate: number;
  latency: number;
  lastUpdate: string;
}

export default function StocksPage() {
  const [, setSocket] = useState<Record<string, unknown> | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [marketData, setMarketData] = useState<Map<string, MarketData>>(
    new Map()
  );
  const [stats, setStats] = useState<Stats>({
    instrumentCount: 0,
    updateRate: 0,
    latency: 0,
    lastUpdate: "Never",
  });

  useEffect(() => {
    // Use shared WebSocket connection to prevent multiple connections
    const socketInstance = createWebSocketConnection({
      onConnect: () => {
        console.log("✅ Stocks page connected to shared WebSocket");
        setIsConnected(true);
      },
      onDisconnect: () => {
        console.log("❌ Stocks page disconnected from shared WebSocket");
        setIsConnected(false);
      },
      onMarketData: (data: MarketData) => {
        setMarketData((prev) => {
          const newMarketData = new Map(prev);
          newMarketData.set(data.securityId, data);
          return newMarketData;
        });
      },
      onMarketDataBatch: (batch: MarketData[]) => {
        setMarketData((prev) => {
          const newMarketData = new Map(prev);
          batch.forEach((item) => {
            newMarketData.set(item.securityId, item);
          });
          return newMarketData;
        });
      },
    });

    socketInstance.on(
      "initialData",
      (data: {
        instruments: Record<string, unknown>[];
        liveData: MarketData[];
      }) => {
        setStats((prev) => ({
          ...prev,
          instrumentCount: data.instruments.length,
        }));

        setMarketData((prev) => {
          const newMarketData = new Map(prev);
          data.liveData.forEach((item: MarketData) => {
            newMarketData.set(item.securityId, item);
          });
          return newMarketData;
        });
      }
    );

    socketInstance.on("status", (data: Record<string, unknown>) => {
      setStats((prev) => ({
        ...prev,
        updateRate: (data.updateRate as number) || 0,
        latency: (data.latency as number) || 0,
        lastUpdate: new Date().toLocaleTimeString(),
      }));
    });

    setSocket(socketInstance as unknown as Record<string, unknown>);

    // Fetch initial stats from the WebSocket server
    const fetchInitialStats = async () => {
      try {
        const response = await fetch("http://localhost:8080/api/data");
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setStats((prev) => ({
          ...prev,
          instrumentCount: data.totalInstruments || data.instruments || 0,
        }));
      } catch (error) {
        console.warn(
          "Could not fetch initial stats from WebSocket server:",
          error
        );
        // Don't show error to user, just log it - WebSocket will provide the data
      }
    };

    // Try to fetch initial stats, but don't block if it fails
    fetchInitialStats();

    return () => {
      // Don't disconnect the shared socket, just remove listeners
      socketInstance.off("initialData");
      socketInstance.off("status");
    };
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20">
      <Navigation />

      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5 pointer-events-none" />

      <main className="relative z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Enhanced Header */}
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center mb-8 space-y-4 lg:space-y-0">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="flex items-center justify-center w-12 h-12 bg-gradient-trading rounded-2xl shadow-lg">
                  <span className="text-2xl">📊</span>
                </div>
                <div>
                  <h1 className="text-3xl font-bold gradient-text">
                    Stocks Market Overview
                  </h1>
                  <p className="text-gray-600 text-sm">
                    Real-time market data and analytics
                  </p>
                </div>
              </div>
              <Link
                href="/market-overview"
                className="hidden sm:inline-flex items-center px-4 py-2 bg-gradient-trading text-white rounded-lg text-sm font-medium hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-200 active:scale-95"
              >
                <span>Detailed View</span>
                <svg
                  className="w-4 h-4 ml-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </Link>
            </div>
            <ConnectionStatus isConnected={isConnected} />
          </div>

          {/* Enhanced Stats */}
          <div className="mb-8">
            <Stats stats={stats} />
          </div>

          {/* Enhanced Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 mb-8">
            {/* Key Indices Sidebar */}
            <div className="lg:col-span-1 space-y-6">
              <div className="sticky top-24">
                <KeyIndices />
              </div>
            </div>

            {/* Main Stocks Column Layout */}
            <div className="lg:col-span-3">
              <div className="fade-in-up">
                <StocksColumnLayout data={Array.from(marketData.values())} />
              </div>
            </div>
          </div>

          {/* Enhanced Market Insights */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-large border border-white/20 p-8">
            <div className="flex items-center space-x-3 mb-6">
              <div className="flex items-center justify-center w-10 h-10 bg-gradient-trading rounded-xl">
                <span className="text-white text-lg">📈</span>
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900">
                  Market Insights
                </h3>
                <p className="text-sm text-gray-600">
                  Live market sentiment analysis
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center p-6 bg-green-50 rounded-xl border border-green-200">
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {
                    Array.from(marketData.values()).filter(
                      (stock) => stock.change > 0
                    ).length
                  }
                </div>
                <div className="text-sm font-medium text-green-700">
                  Stocks Up
                </div>
                <div className="text-xs text-green-600 mt-1">
                  Bullish sentiment
                </div>
              </div>

              <div className="text-center p-6 bg-red-50 rounded-xl border border-red-200">
                <div className="text-3xl font-bold text-red-600 mb-2">
                  {
                    Array.from(marketData.values()).filter(
                      (stock) => stock.change < 0
                    ).length
                  }
                </div>
                <div className="text-sm font-medium text-red-700">
                  Stocks Down
                </div>
                <div className="text-xs text-red-600 mt-1">
                  Bearish sentiment
                </div>
              </div>

              <div className="text-center p-6 bg-gray-50 rounded-xl border border-gray-200">
                <div className="text-3xl font-bold text-gray-600 mb-2">
                  {
                    Array.from(marketData.values()).filter(
                      (stock) => stock.change === 0
                    ).length
                  }
                </div>
                <div className="text-sm font-medium text-gray-700">
                  Unchanged
                </div>
                <div className="text-xs text-gray-600 mt-1">
                  Neutral sentiment
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
