"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseService = void 0;
const pg_1 = require("pg");
class DatabaseService {
    constructor() {
        this.isInitialized = false;
        // Skip database connection during build
        if (process.env.NODE_ENV === "development" &&
            process.env.NEXT_PHASE === "phase-production-build") {
            this.pool = {}; // Mock pool during build
            return;
        }
        // Use the working Supabase connection without SSL issues
        this.pool = new pg_1.Pool({
            connectionString: process.env.POSTGRES_DATABASE_URL,
            ssl: false, // Disable SSL to avoid SASL issues
            max: 20,
            idleTimeoutMillis: 30000,
            connectionTimeoutMillis: 15000,
        });
    }
    static getInstance() {
        if (!DatabaseService.instance) {
            DatabaseService.instance = new DatabaseService();
        }
        return DatabaseService.instance;
    }
    async initialize() {
        // Skip initialization during build
        if (process.env.NODE_ENV === "development" &&
            process.env.NEXT_PHASE === "phase-production-build") {
            console.log("⏭️ Skipping database initialization during build");
            return;
        }
        if (this.isInitialized)
            return;
        try {
            const client = await this.pool.connect();
            client.release();
            // Create tables if they don't exist
            await this.query(`
        CREATE TABLE IF NOT EXISTS watchlists (
          id SERIAL PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          type VARCHAR(50) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS sector_watchlist (
          id SERIAL PRIMARY KEY,
          watchlist_id INTEGER REFERENCES watchlists(id),
          sector_name VARCHAR(255) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS industry_watchlist (
          id SERIAL PRIMARY KEY,
          watchlist_id INTEGER REFERENCES watchlists(id),
          industry_name VARCHAR(255) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS sub_sector_watchlist (
          id SERIAL PRIMARY KEY,
          watchlist_id INTEGER REFERENCES watchlists(id),
          sub_sector_name VARCHAR(255) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS micro_category_watchlist (
          id SERIAL PRIMARY KEY,
          watchlist_id INTEGER REFERENCES watchlists(id),
          micro_category_name VARCHAR(255) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `);
            this.isInitialized = true;
        }
        catch (error) {
            console.error("Error initializing database:", error);
            // Don't throw error during build time
            if (process.env.NODE_ENV !== "production") {
                console.warn("⚠️ Database initialization failed, continuing without database features");
            }
            else {
                throw error;
            }
        }
    }
    async query(text, params) {
        // Return mock result during build
        if (process.env.NODE_ENV === "development" &&
            process.env.NEXT_PHASE === "phase-production-build") {
            return {
                rows: [],
                rowCount: 0,
                command: "",
                oid: 0,
                fields: [],
            };
        }
        try {
            return await this.pool.query(text, params);
        }
        catch (error) {
            console.error("Error executing query:", error);
            throw error;
        }
    }
    // Company lookup methods - Updated for company_list table
    async getCompanyBySymbol(symbol) {
        try {
            const result = await this.query("SELECT * FROM company_list WHERE symbol = $1 LIMIT 1", [symbol]);
            return result.rows[0] || null;
        }
        catch (error) {
            console.error("Error fetching company by symbol:", error);
            throw error;
        }
    }
    async getCompanyByISIN(isin) {
        try {
            const result = await this.query("SELECT * FROM company_list WHERE isin_no = $1 LIMIT 1", [isin]);
            return result.rows[0] || null;
        }
        catch (error) {
            console.error("Error fetching company by ISIN:", error);
            throw error;
        }
    }
    async getCompanyBySecurityId(securityId) {
        try {
            const result = await this.query("SELECT * FROM company_list WHERE bse_security_id = $1 OR nse_security_id = $1 LIMIT 1", [securityId]);
            return result.rows[0] || null;
        }
        catch (error) {
            console.error("Error fetching company by security ID:", error);
            throw error;
        }
    }
    async searchCompanies(filters) {
        try {
            const limit = filters.limit || 50;
            const offset = filters.offset || 0;
            let whereConditions = [];
            let queryParams = [];
            let paramIndex = 1;
            if (filters.sector) {
                whereConditions.push(`sector_name ILIKE $${paramIndex}`);
                queryParams.push(`%${filters.sector}%`);
                paramIndex++;
            }
            if (filters.industry) {
                whereConditions.push(`industry_new_name ILIKE $${paramIndex}`);
                queryParams.push(`%${filters.industry}%`);
                paramIndex++;
            }
            if (filters.search) {
                whereConditions.push(`(
          company_name ILIKE $${paramIndex} OR
          symbol ILIKE $${paramIndex} OR
          bse_security_id ILIKE $${paramIndex} OR
          nse_security_id ILIKE $${paramIndex}
        )`);
                queryParams.push(`%${filters.search}%`);
                paramIndex++;
            }
            const whereClause = whereConditions.length > 0
                ? `WHERE ${whereConditions.join(" AND ")}`
                : "";
            const countQuery = `SELECT COUNT(*) FROM company_list ${whereClause}`;
            const countResult = await this.query(countQuery, queryParams);
            const total = parseInt(countResult.rows[0]?.count || "0");
            const dataQuery = `
        SELECT * FROM company_list
        ${whereClause}
        ORDER BY company_name
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;
            queryParams.push(limit, offset);
            const dataResult = await this.query(dataQuery, queryParams);
            return {
                companies: dataResult.rows,
                total,
                page: Math.floor(offset / limit) + 1,
                totalPages: Math.ceil(total / limit),
            };
        }
        catch (error) {
            console.error("Error searching companies:", error);
            throw error;
        }
    }
    async getSectors() {
        try {
            const result = await this.query("SELECT DISTINCT sector_name FROM company_list WHERE sector_name IS NOT NULL ORDER BY sector_name");
            return result.rows.map((row) => row.sector_name);
        }
        catch (error) {
            console.error("Error fetching sectors:", error);
            throw error;
        }
    }
    async getIndustries(sector) {
        try {
            let query = "SELECT DISTINCT industry_new_name FROM company_list WHERE industry_new_name IS NOT NULL";
            let params = [];
            if (sector) {
                query += " AND sector_name = $1";
                params.push(sector);
            }
            query += " ORDER BY industry_new_name";
            const result = await this.query(query, params);
            return result.rows.map((row) => row.industry_new_name);
        }
        catch (error) {
            console.error("Error fetching industries:", error);
            throw error;
        }
    }
    async getSubSectors(industry) {
        try {
            let query = "SELECT DISTINCT sub_sector FROM company_list WHERE sub_sector IS NOT NULL";
            let params = [];
            if (industry) {
                query += " AND industry_new_name = $1";
                params.push(industry);
            }
            query += " ORDER BY sub_sector";
            const result = await this.query(query, params);
            return result.rows.map((row) => row.sub_sector);
        }
        catch (error) {
            console.error("Error fetching sub-sectors:", error);
            throw error;
        }
    }
    async getMicroCategories(subSector) {
        try {
            let query = "SELECT DISTINCT micro_category FROM company_list WHERE micro_category IS NOT NULL";
            let params = [];
            if (subSector) {
                query += " AND sub_sector = $1";
                params.push(subSector);
            }
            query += " ORDER BY micro_category";
            const result = await this.query(query, params);
            return result.rows.map((row) => row.micro_category);
        }
        catch (error) {
            console.error("Error fetching micro-categories:", error);
            throw error;
        }
    }
    async getCompaniesBySector(sector) {
        try {
            const result = await this.query("SELECT * FROM company_list WHERE sector_name = $1 ORDER BY company_name", [sector]);
            return result.rows;
        }
        catch (error) {
            console.error("Error fetching companies by sector:", error);
            throw error;
        }
    }
    async getCompaniesByIndustry(industry) {
        try {
            const result = await this.query("SELECT * FROM company_list WHERE industry_new_name = $1 ORDER BY company_name", [industry]);
            return result.rows;
        }
        catch (error) {
            console.error("Error fetching companies by industry:", error);
            throw error;
        }
    }
    async getCompaniesBySubSector(subSector) {
        try {
            const result = await this.query("SELECT * FROM company_list WHERE sub_sector = $1 ORDER BY company_name", [subSector]);
            return result.rows;
        }
        catch (error) {
            console.error("Error fetching companies by sub-sector:", error);
            throw error;
        }
    }
    async getCompaniesByMicroCategory(microCategory) {
        try {
            const result = await this.query("SELECT * FROM company_list WHERE micro_category = $1 ORDER BY company_name", [microCategory]);
            return result.rows;
        }
        catch (error) {
            console.error("Error fetching companies by micro-category:", error);
            throw error;
        }
    }
    async getSectorDistribution() {
        try {
            const result = await this.query("SELECT sector_name, COUNT(*) as count FROM company_list WHERE sector_name IS NOT NULL GROUP BY sector_name ORDER BY count DESC");
            return result.rows.map((row) => ({
                sector: row.sector_name,
                count: parseInt(row.count),
            }));
        }
        catch (error) {
            console.error("Error fetching sector distribution:", error);
            throw error;
        }
    }
    // Watchlist methods
    async createWatchlist(name, type) {
        const result = await this.query("INSERT INTO watchlists (name, type) VALUES ($1, $2) RETURNING id", [name, type]);
        return result.rows[0]?.id || 0;
    }
    async getWatchlists() {
        const result = await this.query("SELECT id, name, type FROM watchlists ORDER BY created_at DESC");
        return result.rows;
    }
    async deleteWatchlist(id) {
        await this.query("DELETE FROM watchlists WHERE id = $1", [id]);
    }
    // Sector watchlist methods
    async addToSectorWatchlist(watchlistId, sectorName) {
        await this.query("INSERT INTO sector_watchlist (watchlist_id, sector_name) VALUES ($1, $2)", [watchlistId, sectorName]);
    }
    async getSectorWatchlist(watchlistId) {
        const result = await this.query("SELECT sector_name FROM sector_watchlist WHERE watchlist_id = $1", [watchlistId]);
        return result.rows.map((row) => row.sector_name);
    }
    async removeFromSectorWatchlist(watchlistId, sectorName) {
        await this.query("DELETE FROM sector_watchlist WHERE watchlist_id = $1 AND sector_name = $2", [watchlistId, sectorName]);
    }
    // Industry watchlist methods
    async addToIndustryWatchlist(watchlistId, industryName) {
        await this.query("INSERT INTO industry_watchlist (watchlist_id, industry_name) VALUES ($1, $2)", [watchlistId, industryName]);
    }
    async getIndustryWatchlist(watchlistId) {
        const result = await this.query("SELECT industry_name FROM industry_watchlist WHERE watchlist_id = $1", [watchlistId]);
        return result.rows.map((row) => row.industry_name);
    }
    async removeFromIndustryWatchlist(watchlistId, industryName) {
        await this.query("DELETE FROM industry_watchlist WHERE watchlist_id = $1 AND industry_name = $2", [watchlistId, industryName]);
    }
    // Sub-sector watchlist methods
    async addToSubSectorWatchlist(watchlistId, subSectorName) {
        await this.query("INSERT INTO sub_sector_watchlist (watchlist_id, sub_sector_name) VALUES ($1, $2)", [watchlistId, subSectorName]);
    }
    async getSubSectorWatchlist(watchlistId) {
        const result = await this.query("SELECT sub_sector_name FROM sub_sector_watchlist WHERE watchlist_id = $1", [watchlistId]);
        return result.rows.map((row) => row.sub_sector_name);
    }
    async removeFromSubSectorWatchlist(watchlistId, subSectorName) {
        await this.query("DELETE FROM sub_sector_watchlist WHERE watchlist_id = $1 AND sub_sector_name = $2", [watchlistId, subSectorName]);
    }
    // Micro category watchlist methods
    async addToMicroCategoryWatchlist(watchlistId, microCategoryName) {
        await this.query("INSERT INTO micro_category_watchlist (watchlist_id, micro_category_name) VALUES ($1, $2)", [watchlistId, microCategoryName]);
    }
    async getMicroCategoryWatchlist(watchlistId) {
        const result = await this.query("SELECT micro_category_name FROM micro_category_watchlist WHERE watchlist_id = $1", [watchlistId]);
        return result.rows.map((row) => row.micro_category_name);
    }
    async removeFromMicroCategoryWatchlist(watchlistId, microCategoryName) {
        await this.query("DELETE FROM micro_category_watchlist WHERE watchlist_id = $1 AND micro_category_name = $2", [watchlistId, microCategoryName]);
    }
    // Health check
    async healthCheck() {
        try {
            const result = await this.query("SELECT 1");
            return result.rowCount === 1;
        }
        catch (error) {
            console.error("Error checking database health:", error);
            return false;
        }
    }
    // Get database statistics
    async getStats() {
        try {
            const [watchlistsCount, sectorWatchlistCount, industryWatchlistCount, subSectorWatchlistCount, microCategoryWatchlistCount,] = await Promise.all([
                this.query("SELECT COUNT(*) as count FROM watchlists"),
                this.query("SELECT COUNT(*) as count FROM sector_watchlist"),
                this.query("SELECT COUNT(*) as count FROM industry_watchlist"),
                this.query("SELECT COUNT(*) as count FROM sub_sector_watchlist"),
                this.query("SELECT COUNT(*) as count FROM micro_category_watchlist"),
            ]);
            return {
                watchlists: parseInt(watchlistsCount.rows[0]?.count || "0"),
                sectorWatchlist: parseInt(sectorWatchlistCount.rows[0]?.count || "0"),
                industryWatchlist: parseInt(industryWatchlistCount.rows[0]?.count || "0"),
                subSectorWatchlist: parseInt(subSectorWatchlistCount.rows[0]?.count || "0"),
                microCategoryWatchlist: parseInt(microCategoryWatchlistCount.rows[0]?.count || "0"),
            };
        }
        catch (error) {
            console.error("Error getting database stats:", error);
            throw error;
        }
    }
}
exports.DatabaseService = DatabaseService;
// Don't initialize during build
exports.default = DatabaseService;
//# sourceMappingURL=database.js.map