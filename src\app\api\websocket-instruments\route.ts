import { NextResponse } from "next/server";
import { WebSocketSubscriptionService } from "../../../services/websocket-subscription";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);

    // Get parameters
    const sectors = searchParams.get("sectors")?.split(",") || [];
    const industries = searchParams.get("industries")?.split(",") || [];
    const symbols = searchParams.get("symbols")?.split(",") || [];
    const exchanges = searchParams.get("exchanges")?.split(",") || [
      "NSE",
      "BSE",
    ];
    const limit = parseInt(searchParams.get("limit") || "25000");
    const format = searchParams.get("format") || "json";

    console.log("📡 WebSocket instruments request:", {
      sectors: sectors.length,
      industries: industries.length,
      symbols: symbols.length,
      exchanges,
      limit,
      format,
    });

    let instruments = [];

    // Handle different scenarios
    if (symbols.length > 0) {
      // Get instruments for specific symbols
      for (const exchange of exchanges as ("NSE" | "BSE")[]) {
        const symbolInstruments =
          await WebSocketSubscriptionService.getInstrumentsForIndex(
            symbols,
            exchange
          );
        instruments.push(...symbolInstruments);
      }
    } else if (sectors.length > 0) {
      // Get instruments for specific sectors
      for (const exchange of exchanges as ("NSE" | "BSE")[]) {
        const sectorInstruments =
          await WebSocketSubscriptionService.getInstrumentsByMultipleSectors(
            sectors,
            exchange
          );
        instruments.push(...sectorInstruments);
      }
    } else {
      // Get all instruments with filters
      instruments = await WebSocketSubscriptionService.getWebSocketInstruments({
        limit,
        exchanges: exchanges as string[],
      });
    }

    // Remove duplicates based on SecurityId + ExchangeSegment
    const uniqueInstruments = Array.from(
      new Map(
        instruments.map((inst) => [
          `${inst.SecurityId}-${inst.ExchangeSegment}`,
          inst,
        ])
      ).values()
    );

    console.log(`📊 Generated ${uniqueInstruments.length} unique instruments`);

    // Handle different response formats
    if (format === "dhan") {
      // Format for Dhan WebSocket API
      const dhanFormat = uniqueInstruments.map((inst) => ({
        ExchangeSegment: inst.ExchangeSegment,
        SecurityId: inst.SecurityId,
      }));

      return NextResponse.json({
        success: true,
        data: dhanFormat,
        count: dhanFormat.length,
        meta: {
          sectors: sectors.length,
          industries: industries.length,
          symbols: symbols.length,
          exchanges,
          format: "dhan",
        },
      });
    } else if (format === "csv") {
      // CSV format
      const csvHeader =
        "ExchangeSegment,SecurityId,Symbol,CompanyName,Sector,Industry\n";
      const csvRows = uniqueInstruments
        .map(
          (inst) =>
            `${inst.ExchangeSegment},${inst.SecurityId},${inst.symbol || ""},${inst.companyName || ""},${inst.sector || ""},${inst.industry || ""}`
        )
        .join("\n");

      const csvContent = csvHeader + csvRows;

      return new Response(csvContent, {
        headers: {
          "Content-Type": "text/csv",
          "Content-Disposition":
            "attachment; filename=websocket-instruments.csv",
        },
      });
    } else if (format === "txt") {
      // Text format - one per line
      const textContent = uniqueInstruments
        .map((inst) => `${inst.ExchangeSegment}:${inst.SecurityId}`)
        .join("\n");

      return new Response(textContent, {
        headers: {
          "Content-Type": "text/plain",
          "Content-Disposition":
            "attachment; filename=websocket-instruments.txt",
        },
      });
    } else {
      // Default JSON format with full details
      return NextResponse.json({
        success: true,
        data: uniqueInstruments,
        count: uniqueInstruments.length,
        meta: {
          sectors: sectors.length,
          industries: industries.length,
          symbols: symbols.length,
          exchanges,
          format: "json",
        },
      });
    }
  } catch (error) {
    console.error("❌ WebSocket instruments API error:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const {
      sectors = [],
      industries = [],
      symbols = [],
      exchanges = ["NSE", "BSE"],
      limit = 25000,
      format = "json",
    } = body;

    console.log("📡 WebSocket instruments POST request:", {
      sectors: sectors.length,
      industries: industries.length,
      symbols: symbols.length,
      exchanges,
      limit,
      format,
    });

    let instruments = [];

    // Handle different scenarios
    if (symbols.length > 0) {
      // Get instruments for specific symbols
      for (const exchange of exchanges as ("NSE" | "BSE")[]) {
        const symbolInstruments =
          await WebSocketSubscriptionService.getInstrumentsForIndex(
            symbols,
            exchange
          );
        instruments.push(...symbolInstruments);
      }
    } else if (sectors.length > 0) {
      // Get instruments for specific sectors
      for (const exchange of exchanges as ("NSE" | "BSE")[]) {
        const sectorInstruments =
          await WebSocketSubscriptionService.getInstrumentsByMultipleSectors(
            sectors,
            exchange
          );
        instruments.push(...sectorInstruments);
      }
    } else {
      // Get all instruments with filters
      instruments = await WebSocketSubscriptionService.getWebSocketInstruments({
        limit,
        exchanges: exchanges as string[],
      });
    }

    // Remove duplicates
    const uniqueInstruments = Array.from(
      new Map(
        instruments.map((inst) => [
          `${inst.SecurityId}-${inst.ExchangeSegment}`,
          inst,
        ])
      ).values()
    );

    console.log(`📊 Generated ${uniqueInstruments.length} unique instruments`);

    return NextResponse.json({
      success: true,
      data: uniqueInstruments,
      count: uniqueInstruments.length,
      meta: {
        sectors: sectors.length,
        industries: industries.length,
        symbols: symbols.length,
        exchanges,
        format,
      },
    });
  } catch (error) {
    console.error("❌ WebSocket instruments POST API error:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}
