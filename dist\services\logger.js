"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const config_1 = require("../config");
const fs_1 = require("fs");
const path_1 = require("path");
class Logger {
    constructor() {
        this.logStream = null;
        this.logQueue = [];
        this.isProcessing = false;
        this.initializeLogStream();
    }
    static getInstance() {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }
    initializeLogStream() {
        try {
            const logDir = (0, path_1.join)(process.cwd(), "logs");
            const logFile = (0, path_1.join)(logDir, `app-${new Date().toISOString().split("T")[0]}.log`);
            this.logStream = (0, fs_1.createWriteStream)(logFile, { flags: "a" });
        }
        catch (error) {
            console.error("Failed to initialize log stream:", error);
        }
    }
    getLogLevelValue(level) {
        const levels = {
            error: 0,
            warn: 1,
            info: 2,
            debug: 3,
        };
        return levels[level];
    }
    shouldLog(level) {
        const configLevel = config_1.appConfig.logging.level;
        return (this.getLogLevelValue(level) <=
            this.getLogLevelValue(configLevel));
    }
    formatLogEntry(entry) {
        const { timestamp, level, message, data } = entry;
        const dataStr = data ? `\n${JSON.stringify(data, null, 2)}` : "";
        return `[${timestamp}] ${level.toUpperCase()}: ${message}${dataStr}\n`;
    }
    async processLogQueue() {
        if (this.isProcessing || this.logQueue.length === 0)
            return;
        this.isProcessing = true;
        try {
            while (this.logQueue.length > 0) {
                const entry = this.logQueue.shift();
                if (entry) {
                    const logMessage = this.formatLogEntry(entry);
                    // Write to file if stream is available
                    if (this.logStream) {
                        this.logStream.write(logMessage);
                    }
                    // Also log to console
                    switch (entry.level) {
                        case "error":
                            console.error(logMessage);
                            break;
                        case "warn":
                            console.warn(logMessage);
                            break;
                        case "info":
                            console.info(logMessage);
                            break;
                        case "debug":
                            console.debug(logMessage);
                            break;
                    }
                }
            }
        }
        catch (error) {
            console.error("Error processing log queue:", error);
        }
        finally {
            this.isProcessing = false;
        }
    }
    log(level, message, data) {
        if (!this.shouldLog(level))
            return;
        const entry = {
            timestamp: new Date().toISOString(),
            level,
            message,
            data,
        };
        this.logQueue.push(entry);
        this.processLogQueue();
    }
    error(message, data) {
        this.log("error", message, data);
    }
    warn(message, data) {
        this.log("warn", message, data);
    }
    info(message, data) {
        this.log("info", message, data);
    }
    debug(message, data) {
        this.log("debug", message, data);
    }
    // Log HTTP request
    logRequest(req, res, next) {
        const start = Date.now();
        res.on("finish", () => {
            const duration = Date.now() - start;
            this.info("HTTP Request", {
                method: req.method,
                url: req.originalUrl,
                status: res.statusCode,
                duration: `${duration}ms`,
                ip: req.ip,
                userAgent: req.get("user-agent"),
            });
        });
        next();
    }
    // Log WebSocket events
    logWebSocketEvent(event, data) {
        this.debug("WebSocket Event", { event, data });
    }
    // Log database operations
    logDatabaseOperation(operation, data) {
        this.debug("Database Operation", { operation, data });
    }
    // Close log stream
    close() {
        if (this.logStream) {
            this.logStream.end();
            this.logStream = null;
        }
    }
}
exports.default = Logger;
//# sourceMappingURL=logger.js.map