"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Building2,
  Factory,
  Layers,
  Package,
  Plus,
  Trash2,
} from "lucide-react";

interface WatchlistItem {
  id: number;
  name: string;
  type: "sector" | "industry" | "sub-sector" | "micro-category";
}

export default function WatchlistManager() {
  const [watchlists, setWatchlists] = useState<WatchlistItem[]>([]);
  const [newWatchlistName, setNewWatchlistName] = useState("");
  const [selectedType, setSelectedType] =
    useState<WatchlistItem["type"]>("sector");
  const [, setLoading] = useState(true);

  useEffect(() => {
    fetchWatchlists();
  }, []);

  const fetchWatchlists = async () => {
    try {
      const response = await fetch("/api/watchlists");
      const data = await response.json();
      if (data.success) {
        setWatchlists(data.data);
      }
    } catch (error) {
      console.error("Error fetching watchlists:", error);
    } finally {
      setLoading(false);
    }
  };

  const createWatchlist = async () => {
    if (!newWatchlistName.trim()) return;

    try {
      const response = await fetch("/api/watchlists", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: newWatchlistName,
          type: selectedType,
        }),
      });

      const data = await response.json();
      if (data.success) {
        setNewWatchlistName("");
        fetchWatchlists();
      }
    } catch (error) {
      console.error("Error creating watchlist:", error);
    }
  };

  const deleteWatchlist = async (id: number) => {
    try {
      const response = await fetch(`/api/watchlists/${id}`, {
        method: "DELETE",
      });

      const data = await response.json();
      if (data.success) {
        fetchWatchlists();
      }
    } catch (error) {
      console.error("Error deleting watchlist:", error);
    }
  };

  const getTypeIcon = (type: WatchlistItem["type"]) => {
    switch (type) {
      case "sector":
        return <Building2 className="w-4 h-4" />;
      case "industry":
        return <Factory className="w-4 h-4" />;
      case "sub-sector":
        return <Layers className="w-4 h-4" />;
      case "micro-category":
        return <Package className="w-4 h-4" />;
    }
  };

  const getTypeColor = (type: WatchlistItem["type"]) => {
    switch (type) {
      case "sector":
        return "bg-blue-100 text-blue-600";
      case "industry":
        return "bg-green-100 text-green-600";
      case "sub-sector":
        return "bg-purple-100 text-purple-600";
      case "micro-category":
        return "bg-orange-100 text-orange-600";
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Create New Watchlist</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Input
              placeholder="Watchlist name"
              value={newWatchlistName}
              onChange={(e) => setNewWatchlistName(e.target.value)}
              className="flex-1"
            />
            <select
              value={selectedType}
              onChange={(e) =>
                setSelectedType(e.target.value as WatchlistItem["type"])
              }
              className="px-3 py-2 border rounded-md"
            >
              <option value="sector">Sector</option>
              <option value="industry">Industry</option>
              <option value="sub-sector">Sub-Sector</option>
              <option value="micro-category">Micro Category</option>
            </select>
            <Button onClick={createWatchlist}>
              <Plus className="w-4 h-4 mr-2" />
              Create
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {watchlists.map((watchlist) => (
          <Card key={watchlist.id} className="group">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {watchlist.name}
              </CardTitle>
              <Badge
                variant="outline"
                className={`${getTypeColor(watchlist.type)} flex items-center gap-1`}
              >
                {getTypeIcon(watchlist.type)}
                {watchlist.type}
              </Badge>
            </CardHeader>
            <CardContent>
              <div className="flex justify-end">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => deleteWatchlist(watchlist.id)}
                  className="opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <Trash2 className="w-4 h-4 text-red-500" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
