<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dhan Market Dashboard</title>
    <script src="/socket.io/socket.io.js"></script>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
      }

      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }

      .stats {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
      }

      .stat-card {
        background: white;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        flex: 1;
      }

      .stat-card h3 {
        margin: 0;
        color: #666;
        font-size: 14px;
      }

      .stat-card p {
        margin: 5px 0 0;
        font-size: 24px;
        font-weight: bold;
        color: #333;
      }

      .market-data {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      table {
        width: 100%;
        border-collapse: collapse;
      }

      th,
      td {
        padding: 12px 15px;
        text-align: left;
        border-bottom: 1px solid #eee;
      }

      th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #666;
      }

      tr:hover {
        background-color: #f8f9fa;
      }

      .positive {
        color: #28a745;
      }

      .negative {
        color: #dc3545;
      }

      .connection-status {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
      }

      .connected {
        background-color: #d4edda;
        color: #155724;
      }

      .disconnected {
        background-color: #f8d7da;
        color: #721c24;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>Dhan Market Dashboard</h1>
        <div id="connectionStatus" class="connection-status disconnected">
          Disconnected
        </div>
      </div>

      <div class="stats">
        <div class="stat-card">
          <h3>Connected Clients</h3>
          <p id="clientCount">0</p>
        </div>
        <div class="stat-card">
          <h3>Messages Received</h3>
          <p id="messageCount">0</p>
        </div>
        <div class="stat-card">
          <h3>Instruments</h3>
          <p id="instrumentCount">0</p>
        </div>
      </div>

      <div class="market-data">
        <table>
          <thead>
            <tr>
              <th>Symbol</th>
              <th>LTP</th>
              <th>Change</th>
              <th>Change %</th>
              <th>Volume</th>
              <th>High</th>
              <th>Low</th>
              <th>Open</th>
              <th>Close</th>
            </tr>
          </thead>
          <tbody id="marketDataBody"></tbody>
        </table>
      </div>
    </div>

    <script>
      const socket = io();
      const marketDataBody = document.getElementById("marketDataBody");
      const connectionStatus = document.getElementById("connectionStatus");
      const clientCount = document.getElementById("clientCount");
      const messageCount = document.getElementById("messageCount");
      const instrumentCount = document.getElementById("instrumentCount");
      const marketData = new Map();

      socket.on("connect", () => {
        connectionStatus.textContent = "Connected";
        connectionStatus.className = "connection-status connected";
      });

      socket.on("disconnect", () => {
        connectionStatus.textContent = "Disconnected";
        connectionStatus.className = "connection-status disconnected";
      });

      socket.on("initialData", (data) => {
        instrumentCount.textContent = data.instruments.length;
        data.liveData.forEach(updateMarketData);
      });

      socket.on("marketData", (data) => {
        updateMarketData(data);
      });

      function updateMarketData(data) {
        marketData.set(data.securityId, data);
        renderMarketData();
      }

      function renderMarketData() {
        marketDataBody.innerHTML = "";
        marketData.forEach((data) => {
          const row = document.createElement("tr");
          row.innerHTML = `
            <td>${data.ticker}</td>
            <td>${data.ltp.toFixed(2)}</td>
            <td class="${
              data.change >= 0 ? "positive" : "negative"
            }">${data.change.toFixed(2)}</td>
            <td class="${
              data.changePercent >= 0 ? "positive" : "negative"
            }">${data.changePercent.toFixed(2)}%</td>
            <td>${data.volume.toLocaleString()}</td>
            <td>${data.high.toFixed(2)}</td>
            <td>${data.low.toFixed(2)}</td>
            <td>${data.open.toFixed(2)}</td>
            <td>${data.close.toFixed(2)}</td>
          `;
          marketDataBody.appendChild(row);
        });
      }

      // Fetch initial stats
      fetch("/api/data")
        .then((response) => response.json())
        .then((data) => {
          messageCount.textContent = data.messageCount;
          instrumentCount.textContent = data.instruments.length;
        })
        .catch((error) => console.error("Error fetching stats:", error));
    </script>
  </body>
</html>
