import * as React from "react";
import { cn } from "@/lib/utils";

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?:
    | "default"
    | "secondary"
    | "destructive"
    | "outline"
    | "success"
    | "warning"
    | "info"
    | "gradient";
  size?: "sm" | "default" | "lg";
}

function Badge({
  className,
  variant = "default",
  size = "default",
  ...props
}: BadgeProps) {
  const variants = {
    default:
      "border-transparent bg-primary-600 text-white hover:bg-primary-700 shadow-sm",
    secondary: "border-transparent bg-gray-100 text-gray-800 hover:bg-gray-200",
    destructive:
      "border-transparent bg-red-600 text-white hover:bg-red-700 shadow-sm",
    outline: "text-gray-700 border-gray-300 bg-white hover:bg-gray-50",
    success:
      "border-transparent bg-green-600 text-white hover:bg-green-700 shadow-sm",
    warning:
      "border-transparent bg-yellow-500 text-white hover:bg-yellow-600 shadow-sm",
    info: "border-transparent bg-blue-600 text-white hover:bg-blue-700 shadow-sm",
    gradient:
      "border-transparent bg-gradient-trading text-white hover:shadow-lg hover:shadow-blue-500/25",
  };

  const sizes = {
    sm: "px-2 py-0.5 text-xs",
    default: "px-2.5 py-0.5 text-xs",
    lg: "px-3 py-1 text-sm",
  };

  return (
    <div
      className={cn(
        "inline-flex items-center rounded-full border font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2",
        variants[variant],
        sizes[size],
        className
      )}
      {...props}
    />
  );
}

export { Badge };
