{"version": 3, "file": "MarketFeedServer.d.ts", "sourceRoot": "", "sources": ["../../src/server/MarketFeedServer.ts"], "names": [], "mappings": "AAIA,OAAO,eAAe,CAAC;AAsCvB,qBAAa,oBAAoB;IAC/B,OAAO,CAAC,WAAW,CAAS;IAC5B,OAAO,CAAC,QAAQ,CAAS;IACzB,OAAO,CAAC,gBAAgB,CAAS;IACjC,OAAO,CAAC,IAAI,CAAS;IAErB,OAAO,CAAC,EAAE,CAA0B;IACpC,OAAO,CAAC,WAAW,CAAkB;IACrC,OAAO,CAAC,YAAY,CAAa;IACjC,OAAO,CAAC,WAAW,CAAoB;IAGvC,OAAO,CAAC,QAAQ,CAAsC;IAGtD,OAAO,CAAC,GAAG,CAAsB;IACjC,OAAO,CAAC,MAAM,CAAM;IACpB,OAAO,CAAC,EAAE,CAAS;IAGnB,OAAO,CAAC,kBAAkB,CAAa;IACvC,OAAO,CAAC,oBAAoB,CAAa;IAGzC,OAAO,CAAC,aAAa,CAAgB;;IAyBrC;;OAEG;IACH,OAAO,CAAC,cAAc;IAyBtB;;OAEG;YACW,eAAe;IAiK7B;;OAEG;IACH,OAAO,CAAC,cAAc;IA6ctB;;OAEG;IACH,OAAO,CAAC,mBAAmB;IAkB3B;;OAEG;IACG,mBAAmB,IAAI,OAAO,CAAC,IAAI,CAAC;IAgF1C;;OAEG;IACH,OAAO,CAAC,sBAAsB;IAsB9B;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAwCxB;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAkExB;;OAEG;IACH,OAAO,CAAC,eAAe;IA6HvB;;OAEG;IACH,OAAO,CAAC,sBAAsB;IAQ9B,OAAO,CAAC,kBAAkB;IAgB1B,OAAO,CAAC,SAAS;IAuBjB,OAAO,CAAC,WAAW;IAuBnB;;OAEG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAuD5B;;OAEG;IACH,OAAO,CAAC,qBAAqB;IAyB7B;;OAEG;IACG,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;IAgF/B;;OAEG;IACH,SAAS,IAAI;QACX,SAAS,EAAE,OAAO,CAAC;QACnB,WAAW,EAAE,MAAM,CAAC;QACpB,QAAQ,EAAE,MAAM,CAAC;QACjB,MAAM,EAAE,MAAM,CAAC;QACf,MAAM,EAAE,MAAM,CAAC;KAChB;CASF"}