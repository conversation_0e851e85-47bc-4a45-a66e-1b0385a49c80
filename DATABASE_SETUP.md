# 🗄️ Database Setup Guide

## ✅ Current Status
Your Supabase database is **WORKING** and **POPULATED** with 4,454 companies!

## 🔗 Working Connection
```env
POSTGRES_DATABASE_URL="***************************************************************/postgres"
```

## 📊 Database Schema

### Main Table: `company_list`
```sql
CREATE TABLE company_list (
  id SERIAL PRIMARY KEY,
  company_name VARCHAR(500) NOT NULL,
  isin_no VARCHAR(20),
  instrument VARCHAR(50),
  sector_name VARCHAR(200),
  industry_new_name VARCHAR(200),
  sub_sector VARCHAR(200),
  micro_category VARCHAR(200),
  bse_security_id VARCHAR(20),
  nse_security_id VARCHAR(20),
  symbol VARCHAR(50),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Supporting Tables:
- `watchlist_sectors` - Sector-wise watchlists
- `watchlist_industries` - Industry-wise watchlists  
- `user_watchlists` - Custom user watchlists

## 🚀 Quick Start Commands

### 1. Check Database Status
```bash
npm run db:check
```

### 2. Run Migration (if needed)
```bash
npm run db:migrate
```

### 3. Import Company Data (if needed)
```bash
npm run db:import
```

### 4. Test WebSocket Instruments
```bash
npm run db:test
```

### 5. Complete Setup (migrate + import)
```bash
npm run db:setup
```

## 📈 Data Summary
- **Total Companies**: 4,454
- **Sectors**: 14 (Consumer Discretionary, Industrials, Financial Services, etc.)
- **Industries**: 25+ categories
- **Dual Listed**: 2,102 companies (both NSE & BSE)
- **NSE Only**: 1 company
- **BSE Only**: 2,351 companies

## 🔧 Working Scripts

### ✅ Keep These Files:
- `scripts/migrate-database.js` - Database migration
- `import-to-supabase-working.js` - Data import
- `scripts/check-db-schema.ts` - Schema verification
- `scripts/test-websocket-instruments.js` - WebSocket testing

### ❌ Removed Files:
- All old migration scripts
- Non-working connection tests
- Duplicate import scripts

## 🏢 Top Sectors by Company Count:
1. **Consumer Discretionary**: 1,129 companies
2. **Industrials**: 747 companies
3. **Financial Services**: 643 companies
4. **Commodities**: 487 companies
5. **Services**: 443 companies
6. **FMCG**: 338 companies
7. **Healthcare**: 268 companies
8. **Information Technology**: 222 companies
9. **Utilities**: 56 companies
10. **Energy**: 53 companies

## 🔍 Query Examples

### Get Company by Symbol
```sql
SELECT * FROM company_list WHERE symbol = 'RELIANCE';
```

### Get Companies by Sector
```sql
SELECT * FROM company_list WHERE sector_name = 'Financial Services';
```

### Get Dual Listed Companies
```sql
SELECT * FROM company_list 
WHERE nse_security_id IS NOT NULL 
AND bse_security_id IS NOT NULL;
```

### WebSocket Ready Companies
```sql
SELECT company_name, symbol, nse_security_id, bse_security_id 
FROM company_list 
WHERE nse_security_id IS NOT NULL OR bse_security_id IS NOT NULL;
```

## 🌐 WebSocket Integration

### NSE Format:
```json
{
  "ExchangeSegment": "NSE_EQ",
  "SecurityId": "nse_security_id_from_database"
}
```

### BSE Format:
```json
{
  "ExchangeSegment": "BSE_EQ", 
  "SecurityId": "bse_security_id_from_database"
}
```

## 🔄 Application Integration

Your application can now:
1. ✅ Query companies by symbol, sector, industry
2. ✅ Get NSE/BSE security IDs for WebSocket connections
3. ✅ Build sector-wise dashboards
4. ✅ Create watchlists
5. ✅ Filter by exchange (NSE/BSE)

## 🛠️ Troubleshooting

### Connection Issues:
1. Check `.env` file has correct `POSTGRES_DATABASE_URL`
2. Verify Supabase project is active
3. Run `npm run db:check` to test connection

### Data Issues:
1. Run `npm run db:import` to re-import data
2. Check Excel file `Companylist.xlsx` exists
3. Verify table structure with `npm run db:check`

## 📝 Environment Variables Required:
```env
POSTGRES_DATABASE_URL="***************************************************************/postgres"
DATABASE_URL="***************************************************************/postgres"
DIRECT_URL="***************************************************************/postgres"
```

## 🎯 Next Steps:
1. Your database is ready for production use
2. All company data is imported and indexed
3. WebSocket integration is ready
4. Application can query real data

**Your Supabase database is fully operational! 🚀**
