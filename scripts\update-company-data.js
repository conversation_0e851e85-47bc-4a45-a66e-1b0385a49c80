import { Client } from "pg";
import dotenv from "dotenv";
import { fileURLToPath } from "url";
import { dirname } from "path";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

dotenv.config();

// Database connection
const client = new Client({
  connectionString: process.env.POSTGRES_DATABASE_URL,
  ssl: {
    rejectUnauthorized: false, // Accept self-signed certificates
  },
});

async function updateCompanyData() {
  try {
    await client.connect();
    console.log("Connected to database");

    // Check if company exists
    const checkSQL = `
      SELECT * FROM companies WHERE isin_no = 'INE049B01025';
    `;
    const checkResult = await client.query(checkSQL);

    if (checkResult.rows.length === 0) {
      // Insert new company
      const insertSQL = `
        INSERT INTO companies (
          isin_no,
          company_name,
          nse_symbol,
          bse_symbol,
          nse_security_id,
          bse_security_id,
          sector_name,
          industry_name
        ) VALUES (
          'INE049B01025',
          'Wockhardt Limited',
          'WOCKPHARMA',
          'WOCKPHARMA',
          '1921',
          '532300',
          'Healthcare',
          'Healthcare'
        );
      `;
      await client.query(insertSQL);
      console.log("Inserted new company data");
    } else {
      // Update existing company
      const updateSQL = `
        UPDATE companies
        SET 
          nse_symbol = 'WOCKPHARMA',
          bse_symbol = 'WOCKPHARMA',
          nse_security_id = '1921',
          bse_security_id = '532300',
          sector_name = 'Healthcare',
          industry_name = 'Healthcare',
          company_name = 'Wockhardt Limited'
        WHERE isin_no = 'INE049B01025';
      `;
      await client.query(updateSQL);
      console.log("Updated existing company data");
    }

    console.log("Update completed successfully");
  } catch (error) {
    console.error("Error during update:", error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

updateCompanyData();
