// ============================================================================
// DATA SERVICE - Optimized Database Operations with Caching
// ============================================================================

import { Pool, PoolClient } from "pg";
import {
  DataService as IDataService,
  MarketData,
  Watchlist,
  WatchlistItem,
  Company,
} from "@/types";
import { cacheService, CacheKeys } from "./CacheService";
import { logger } from "./LoggerService";

export class DataService implements IDataService {
  private pool: Pool;
  private isConnected: boolean = false;

  constructor(connectionConfig: {
    host: string;
    port: number;
    database: string;
    user: string;
    password: string;
    ssl?: any;
  }) {
    this.pool = new Pool({
      ...connectionConfig,
      max: 20, // Maximum number of clients in the pool
      idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
      connectionTimeoutMillis: 10000, // Increased timeout for cloud databases
      maxUses: 7500, // Close (and replace) a connection after it has been used 7500 times
    });

    this.pool.on("connect", () => {
      this.isConnected = true;
      logger.info("Database client connected");
    });

    this.pool.on("error", (err) => {
      this.isConnected = false;
      logger.error("Database pool error", { error: err.message });
    });
  }

  /**
   * Get market data for a specific security
   */
  async getMarketData(securityId: string): Promise<MarketData | null> {
    const cacheKey = CacheKeys.marketData(securityId);

    // Try cache first
    const cached = cacheService.get<MarketData>(cacheKey);
    if (cached) {
      logger.debug("Market data served from cache", { securityId });
      return cached;
    }

    try {
      const client = await this.pool.connect();
      try {
        const query = `
          SELECT
            c.symbol as ticker,
            c.nse_security_id as security_id,
            'NSE' as exchange,
            1 as exchange_code,
            0 as ltp,
            0 as change,
            0 as change_percent,
            0 as volume,
            0 as high,
            0 as low,
            0 as open,
            0 as close,
            EXTRACT(EPOCH FROM NOW()) * 1000 as timestamp
          FROM company_list c
          WHERE c.nse_security_id = $1 OR c.bse_security_id = $1
          LIMIT 1
        `;

        const result = await client.query(query, [securityId]);

        if (result.rows.length === 0) {
          return null;
        }

        const marketData: MarketData = {
          ticker: result.rows[0].ticker || `UNKNOWN_${securityId}`,
          securityId: securityId,
          exchange: result.rows[0].exchange,
          exchangeCode: result.rows[0].exchange_code,
          ltp: result.rows[0].ltp,
          change: result.rows[0].change,
          changePercent: result.rows[0].change_percent,
          volume: result.rows[0].volume,
          high: result.rows[0].high,
          low: result.rows[0].low,
          open: result.rows[0].open,
          close: result.rows[0].close,
          timestamp: result.rows[0].timestamp,
        };

        // Cache for 30 seconds
        cacheService.set(cacheKey, marketData, 30000);

        logger.debug("Market data fetched from database", { securityId });
        return marketData;
      } finally {
        client.release();
      }
    } catch (error) {
      logger.error("Error fetching market data", {
        securityId,
        error: (error as Error).message,
      });
      return null;
    }
  }

  /**
   * Get indices data with caching
   */
  async getIndices(limit: number = 100): Promise<MarketData[]> {
    const cacheKey = CacheKeys.indices(limit);

    // Try cache first
    const cached = cacheService.get<MarketData[]>(cacheKey);
    if (cached) {
      logger.debug("Indices data served from cache", {
        limit,
        count: cached.length,
      });
      return cached;
    }

    try {
      const client = await this.pool.connect();
      try {
        const query = `
          SELECT
            c.symbol as ticker,
            c.nse_security_id as security_id,
            'NSE' as exchange,
            1 as exchange_code
          FROM company_list c
          WHERE c.sector_name LIKE '%Index%' OR c.industry_new_name LIKE '%Index%'
          ORDER BY c.company_name
          LIMIT $1
        `;

        const result = await client.query(query, [limit]);

        const indices: MarketData[] = result.rows.map((row) => ({
          ticker: row.ticker || `INDEX_${row.security_id}`,
          securityId: row.security_id?.toString() || "0",
          exchange: row.exchange,
          exchangeCode: row.exchange_code,
          ltp: 0,
          change: 0,
          changePercent: 0,
          volume: 0,
          high: 0,
          low: 0,
          open: 0,
          close: 0,
          timestamp: Date.now(),
        }));

        // Cache for 5 minutes
        cacheService.set(cacheKey, indices, 300000);

        logger.debug("Indices data fetched from database", {
          limit,
          count: indices.length,
        });
        return indices;
      } finally {
        client.release();
      }
    } catch (error) {
      logger.error("Error fetching indices data", {
        limit,
        error: (error as Error).message,
      });
      return [];
    }
  }

  /**
   * Get all watchlists
   */
  async getWatchlists(): Promise<Watchlist[]> {
    const cacheKey = CacheKeys.watchlist(0); // Use 0 for all watchlists

    // Try cache first
    const cached = cacheService.get<Watchlist[]>(cacheKey);
    if (cached) {
      logger.debug("Watchlists served from cache", { count: cached.length });
      return cached;
    }

    try {
      const client = await this.pool.connect();
      try {
        const query = `
          SELECT id, name, description, created_at, updated_at
          FROM watchlists 
          ORDER BY created_at DESC
        `;

        const result = await client.query(query);

        const watchlists: Watchlist[] = result.rows.map((row) => ({
          id: row.id,
          name: row.name,
          description: row.description,
          created_at: row.created_at,
          updated_at: row.updated_at,
        }));

        // Cache for 10 minutes
        cacheService.set(cacheKey, watchlists, 600000);

        logger.debug("Watchlists fetched from database", {
          count: watchlists.length,
        });
        return watchlists;
      } finally {
        client.release();
      }
    } catch (error) {
      logger.error("Error fetching watchlists", {
        error: (error as Error).message,
      });
      return [];
    }
  }

  /**
   * Get watchlist items
   */
  async getWatchlistItems(watchlistId: number): Promise<WatchlistItem[]> {
    const cacheKey = CacheKeys.watchlistItems(watchlistId);

    // Try cache first
    const cached = cacheService.get<WatchlistItem[]>(cacheKey);
    if (cached) {
      logger.debug("Watchlist items served from cache", {
        watchlistId,
        count: cached.length,
      });
      return cached;
    }

    try {
      const client = await this.pool.connect();
      try {
        const query = `
          SELECT id, watchlist_id, security_id, exchange, ticker, added_at
          FROM watchlist_items 
          WHERE watchlist_id = $1
          ORDER BY added_at DESC
        `;

        const result = await client.query(query, [watchlistId]);

        const items: WatchlistItem[] = result.rows.map((row) => ({
          id: row.id,
          watchlist_id: row.watchlist_id,
          security_id: row.security_id,
          exchange: row.exchange,
          ticker: row.ticker,
          added_at: row.added_at,
        }));

        // Cache for 5 minutes
        cacheService.set(cacheKey, items, 300000);

        logger.debug("Watchlist items fetched from database", {
          watchlistId,
          count: items.length,
        });
        return items;
      } finally {
        client.release();
      }
    } catch (error) {
      logger.error("Error fetching watchlist items", {
        watchlistId,
        error: (error as Error).message,
      });
      return [];
    }
  }

  /**
   * Get companies with pagination and filtering
   */
  async getCompanies(
    limit: number = 100,
    offset: number = 0,
    sector?: string,
    exchange?: string,
    search?: string
  ): Promise<{ companies: Company[]; total: number }> {
    try {
      const client = await this.pool.connect();
      try {
        let whereClause = "";
        const params: any[] = [limit, offset];
        let paramIndex = 3;

        if (sector) {
          whereClause += ` WHERE sector_name = $${paramIndex}`;
          params.push(sector);
          paramIndex++;
        }

        if (exchange) {
          whereClause += sector ? " AND" : " WHERE";
          if (exchange === "NSE") {
            whereClause += ` (symbol IS NOT NULL AND nse_security_id IS NOT NULL AND symbol != '-' AND nse_security_id != '-')`;
          } else if (exchange === "BSE") {
            whereClause += ` (bse_security_id IS NOT NULL AND bse_security_id != '-')`;
          }
        }

        if (search) {
          whereClause += whereClause ? " AND" : " WHERE";
          whereClause += ` (
            symbol = $${paramIndex} OR
            company_name = $${paramIndex}
          )`;
          params.push(search);
          paramIndex++;
        }

        const countQuery = `SELECT COUNT(*) FROM company_list${whereClause}`;
        const dataQuery = `
          SELECT * FROM company_list${whereClause}
          ORDER BY company_name
          LIMIT $1 OFFSET $2
        `;

        const [countResult, dataResult] = await Promise.all([
          client.query(countQuery, params.slice(2)),
          client.query(dataQuery, params),
        ]);

        const companies: Company[] = dataResult.rows;
        const total = parseInt(countResult.rows[0].count);

        logger.debug("Companies fetched from database", {
          limit,
          offset,
          sector,
          exchange,
          search,
          count: companies.length,
          total,
        });

        return { companies, total };
      } finally {
        client.release();
      }
    } catch (error) {
      logger.error("Error fetching companies", {
        limit,
        offset,
        sector,
        exchange,
        search,
        error: (error as Error).message,
      });
      return { companies: [], total: 0 };
    }
  }

  /**
   * Check database connection
   */
  async checkConnection(): Promise<boolean> {
    try {
      const client = await this.pool.connect();
      try {
        await client.query("SELECT 1");
        this.isConnected = true;
        return true;
      } finally {
        client.release();
      }
    } catch (error) {
      this.isConnected = false;
      logger.error("Database connection check failed", {
        error: (error as Error).message,
      });
      return false;
    }
  }

  /**
   * Get connection status
   */
  isConnectionHealthy(): boolean {
    return this.isConnected;
  }

  /**
   * Get pool statistics
   */
  getPoolStats(): {
    totalCount: number;
    idleCount: number;
    waitingCount: number;
  } {
    return {
      totalCount: this.pool.totalCount,
      idleCount: this.pool.idleCount,
      waitingCount: this.pool.waitingCount,
    };
  }

  /**
   * Close all connections
   */
  async close(): Promise<void> {
    await this.pool.end();
    this.isConnected = false;
    logger.info("Database pool closed");
  }
}

// Parse PostgreSQL URL or use individual environment variables
function parsePostgresConfig() {
  // Try environment variable first, then fallback to hardcoded
  const postgresUrl =
    process.env.POSTGRES_DATABASE_URL || process.env.DATABASE_URL;

  if (postgresUrl) {
    try {
      const url = new URL(postgresUrl);
      return {
        host: url.hostname,
        port: parseInt(url.port) || 5432,
        database: url.pathname.slice(1), // Remove leading slash
        user: url.username,
        password: url.password,
        ssl: false, // Disable SSL to avoid SASL issues
      };
    } catch (error) {
      console.error(
        "Failed to parse connection string from environment:",
        error
      );
    }
  }

  // Fallback to individual environment variables if URL parsing fails
  return {
    host: process.env.DB_HOST || "localhost",
    port: parseInt(process.env.DB_PORT || "5432"),
    database: process.env.DB_NAME || "dhan_websocket",
    user: process.env.DB_USER || "postgres",
    password: process.env.DB_PASSWORD || "password",
    ssl: false,
  };
}

// Singleton instance
export const dataService = new DataService(parsePostgresConfig());

export default DataService;
