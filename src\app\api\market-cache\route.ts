// Simple market cache API without NextResponse

// In-memory cache for market data
let marketDataCache: unknown = null;
let lastSavedTime: Date | null = null;
let marketDate: string | null = null;

// Market hours: 9:00 AM to 3:30 PM IST
const isMarketOpen = () => {
  const now = new Date();
  const istTime = new Date(now.getTime() + 5.5 * 60 * 60 * 1000); // Convert to IST
  const hours = istTime.getHours();
  const minutes = istTime.getMinutes();
  const currentTime = hours * 100 + minutes;

  // Market open: 9:00 AM (900) to 3:30 PM (1530)
  return currentTime >= 900 && currentTime <= 1530;
};

const isWeekday = () => {
  const now = new Date();
  const day = now.getDay();
  return day >= 1 && day <= 5; // Monday to Friday
};

const getTodayDate = () => {
  return new Date().toISOString().split("T")[0]; // YYYY-MM-DD
};

export async function POST(request: Request): Promise<Response> {
  try {
    const { action, data } = await request.json();

    if (action === "save-market-data") {
      // Save current WebSocket feed data in memory
      marketDataCache = data;
      lastSavedTime = new Date();
      marketDate = getTodayDate();

      console.log(
        `📦 Market data cached at ${lastSavedTime.toLocaleTimeString()}`
      );
      console.log(
        `📊 Cached ${Array.isArray(data?.latestData) ? data.latestData.length : 0} stocks`
      );

      return new Response(
        JSON.stringify({
          success: true,
          message: "Market data cached successfully",
          saved_at: lastSavedTime,
          records_count: Array.isArray(data?.latestData)
            ? data.latestData.length
            : 0,
          market_date: marketDate,
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: false,
        message: "Invalid action",
      }),
      {
        headers: { "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("Market cache error:", error);
    return new Response(
      JSON.stringify({
        success: false,
        message: "Failed to cache market data",
        error: error instanceof Error ? error.message : "Unknown error",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}

export async function GET(request: Request): Promise<Response> {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get("action");

    if (action === "market-status") {
      const marketOpen = isMarketOpen() && isWeekday();
      const today = getTodayDate();

      return new Response(
        JSON.stringify({
          success: true,
          market_open: marketOpen,
          is_weekday: isWeekday(),
          current_time: new Date().toISOString(),
          has_cached_data: marketDataCache !== null,
          cache_date: marketDate,
          is_cache_today: marketDate === today,
          message: marketOpen
            ? "Market is open - using live data"
            : "Market is closed - using cached data",
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    if (action === "get-data") {
      const marketOpen = isMarketOpen() && isWeekday();
      const today = getTodayDate();

      // If market is open, try to get live data
      if (marketOpen) {
        try {
          const liveResponse = await fetch("http://localhost:8080/api/data", {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
            // Add timeout
            signal: AbortSignal.timeout(5000),
          });

          if (liveResponse.ok) {
            const liveData = await liveResponse.json();

            // Cache the live data for later use
            marketDataCache = liveData;
            lastSavedTime = new Date();
            marketDate = today;

            return new Response(
              JSON.stringify({
                success: true,
                data: liveData,
                is_live: true,
                source: "live_websocket",
                timestamp: new Date().toISOString(),
              }),
              {
                headers: { "Content-Type": "application/json" },
              }
            );
          }
        } catch (error) {
          console.log("Live data not available, using cached data");
        }
      }

      // Market is closed or live data unavailable - use cached data
      if (marketDataCache) {
        return new Response(
          JSON.stringify({
            success: true,
            data: marketDataCache,
            is_live: false,
            source: "cached_data",
            saved_at: lastSavedTime,
            market_date: marketDate,
            timestamp: new Date().toISOString(),
          }),
          {
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // No cached data available
      return new Response(
        JSON.stringify({
          success: false,
          message: "No market data available - WebSocket not connected yet",
          market_open: marketOpen,
          has_cache: false,
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    if (action === "clear-cache") {
      // Clear cache (useful for testing or manual reset)
      marketDataCache = null;
      lastSavedTime = null;
      marketDate = null;

      return new Response(
        JSON.stringify({
          success: true,
          message: "Market data cache cleared",
        }),
        {
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: false,
        message: "Invalid action",
      }),
      {
        headers: { "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("Market cache fetch error:", error);
    return new Response(
      JSON.stringify({
        success: false,
        message: "Failed to fetch market data",
        error: error instanceof Error ? error.message : "Unknown error",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}
