const axios = require('axios');
require('dotenv').config();

const BASE_URL = 'http://localhost:8080';

async function testDatabaseWebSocket() {
  console.log('🧪 Testing Database-Driven WebSocket Server\n');

  try {
    // Test 1: Check database statistics
    console.log('📊 Test 1: Database Statistics');
    try {
      const statsResponse = await axios.get(`${BASE_URL}/api/database-stats`);
      if (statsResponse.data.success) {
        const stats = statsResponse.data.data;
        console.log('✅ Database Stats:');
        console.log(`   Total Companies: ${stats.totalCompanies}`);
        console.log(`   Total Sectors: ${stats.totalSectors}`);
        console.log(`   Total Industries: ${stats.totalIndustries}`);
        console.log(`   Companies with NSE Symbol: ${stats.companiesWithNSESymbol}`);
        console.log(`   Companies with BSE ID: ${stats.companiesWithBSEId}`);
        
        console.log('\n📈 Top Sectors:');
        stats.sectorDistribution.slice(0, 5).forEach((sector, index) => {
          console.log(`   ${index + 1}. ${sector.sector}: ${sector.count} companies (${sector.percentage}%)`);
        });
      }
    } catch (error) {
      console.log('❌ Database stats test failed:', error.message);
    }

    // Test 2: Get available sectors
    console.log('\n📋 Test 2: Available Sectors');
    try {
      const sectorsResponse = await axios.get(`${BASE_URL}/api/sectors`);
      if (sectorsResponse.data.success) {
        const sectors = sectorsResponse.data.data;
        console.log(`✅ Found ${sectors.length} sectors:`);
        sectors.slice(0, 10).forEach((sector, index) => {
          console.log(`   ${index + 1}. ${sector}`);
        });
        if (sectors.length > 10) {
          console.log(`   ... and ${sectors.length - 10} more`);
        }
      }
    } catch (error) {
      console.log('❌ Sectors test failed:', error.message);
    }

    // Test 3: Get WebSocket instruments for Bank Nifty
    console.log('\n🏦 Test 3: Bank Nifty WebSocket Instruments');
    try {
      const bankNiftyResponse = await axios.get(`${BASE_URL}/api/websocket-instruments?action=by-index&index=BANKNIFTY`);
      if (bankNiftyResponse.data.success) {
        const data = bankNiftyResponse.data.data;
        console.log(`✅ Bank Nifty Instruments: ${data.totalInstruments}`);
        console.log('📋 Sample instruments:');
        data.instruments.slice(0, 5).forEach((instrument, index) => {
          console.log(`   ${index + 1}. ${instrument.companyName} (${instrument.symbol})`);
          console.log(`      WebSocket: { "ExchangeSegment": "${instrument.ExchangeSegment}", "SecurityId": "${instrument.SecurityId}" }`);
        });
        
        console.log('\n📤 WebSocket Subscription Message:');
        console.log(JSON.stringify(data.subscriptionMessage, null, 2));
      }
    } catch (error) {
      console.log('❌ Bank Nifty test failed:', error.message);
    }

    // Test 4: Get instruments by sector
    console.log('\n💰 Test 4: Financial Services Sector Instruments');
    try {
      const sectorResponse = await axios.get(`${BASE_URL}/api/websocket-instruments?action=by-sectors&sectors=Financial Services&exchange=NSE&limit=10`);
      if (sectorResponse.data.success) {
        const data = sectorResponse.data.data;
        console.log(`✅ Financial Services Instruments: ${data.totalInstruments} total`);
        console.log('📋 Sample instruments (first 10):');
        data.instruments.slice(0, 10).forEach((instrument, index) => {
          console.log(`   ${index + 1}. ${instrument.companyName} (${instrument.symbol})`);
          console.log(`      NSE Security ID: ${instrument.SecurityId}`);
        });
        
        console.log(`\n🔗 WebSocket Connections: ${data.totalConnections}`);
        data.connections.forEach((conn, index) => {
          console.log(`   Connection ${conn.connectionId}: ${conn.count} instruments`);
        });
      }
    } catch (error) {
      console.log('❌ Financial Services test failed:', error.message);
    }

    // Test 5: Test current server data
    console.log('\n🔄 Test 5: Current Server Data');
    try {
      const dataResponse = await axios.get(`${BASE_URL}/api/data`);
      console.log('✅ Server Status:');
      console.log(`   Connected: ${dataResponse.data.connected}`);
      console.log(`   Instruments: ${dataResponse.data.instruments}`);
      console.log(`   Subscription Type: ${dataResponse.data.subscriptionType}`);
      console.log(`   Active Instruments: ${dataResponse.data.activeInstruments}`);
      console.log(`   Latest Data Count: ${dataResponse.data.latestData.length}`);
      
      if (dataResponse.data.latestData.length > 0) {
        console.log('\n📈 Sample Market Data:');
        dataResponse.data.latestData.slice(0, 3).forEach((stock, index) => {
          console.log(`   ${index + 1}. ${stock.ticker}: ₹${stock.ltp} (${stock.changePercent > 0 ? '+' : ''}${stock.changePercent}%)`);
        });
      }
    } catch (error) {
      console.log('❌ Server data test failed:', error.message);
    }

    // Test 6: Test reload instruments
    console.log('\n🔄 Test 6: Reload Instruments');
    try {
      const reloadResponse = await axios.post(`${BASE_URL}/api/reload-instruments`);
      if (reloadResponse.data.success) {
        console.log('✅ Instruments reloaded successfully');
        console.log(`   New instrument count: ${reloadResponse.data.instrumentCount}`);
      }
    } catch (error) {
      console.log('❌ Reload instruments test failed:', error.message);
    }

    console.log('\n🎉 Database WebSocket testing completed!');
    
    console.log('\n💡 Configuration Tips:');
    console.log('1. Set SECTORS in .env to load specific sectors');
    console.log('2. Set MAX_INSTRUMENTS to limit subscription count');
    console.log('3. Set PREFERRED_EXCHANGE to NSE or BSE');
    console.log('4. Leave INSTRUMENTS empty to use database');
    console.log('5. Use /api/reload-instruments to refresh subscriptions');

  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
  }
}

// Helper function to test specific configuration
async function testConfiguration(sectors, maxInstruments = 1000, exchange = 'NSE') {
  console.log(`\n🧪 Testing Configuration: ${sectors.join(', ')} (${exchange}, max: ${maxInstruments})`);
  
  try {
    const response = await axios.post(`${BASE_URL}/api/websocket-instruments`, {
      sectors: sectors,
      exchange: exchange,
      requestCode: 17
    });

    if (response.data.success) {
      const data = response.data.data;
      console.log(`✅ Generated ${data.totalInstruments} instruments`);
      console.log(`📡 Requires ${data.totalConnections} WebSocket connections`);
      
      data.connections.forEach((conn, index) => {
        console.log(`   Connection ${conn.connectionId}: ${conn.instruments.length} instruments`);
      });

      return data;
    }
  } catch (error) {
    console.log(`❌ Configuration test failed: ${error.message}`);
  }
}

// Run tests
if (require.main === module) {
  testDatabaseWebSocket().then(() => {
    // Test some specific configurations
    console.log('\n🔧 Testing Specific Configurations:');
    
    Promise.all([
      testConfiguration(['Financial Services'], 500),
      testConfiguration(['Information Technology', 'Healthcare'], 1000),
      testConfiguration(['Consumer Discretionary'], 2000, 'BSE')
    ]).then(() => {
      console.log('\n✅ All configuration tests completed!');
    });
  });
}

module.exports = { testDatabaseWebSocket, testConfiguration };
