import { DatabaseService } from "./database";

const db = DatabaseService.getInstance();

export interface WebSocketInstrument {
  ExchangeSegment: string;
  SecurityId: string;
  symbol?: string;
  companyName?: string;
  sector?: string;
  industry?: string;
}

export interface WebSocketConnection {
  connectionId: number;
  instruments: WebSocketInstrument[];
  count: number;
}

export interface Company {
  id: number;
  company_name: string;
  nse_symbol?: string;
  bse_symbol?: string;
  nse_security_id?: string;
  bse_security_id?: string;
  sector_name?: string;
  industry_name?: string;
}

export class WebSocketSubscriptionService {
  private static readonly MAX_CONNECTIONS = 5;
  private static readonly MAX_INSTRUMENTS_PER_CONNECTION = 5000;

  /**
   * Get WebSocket instruments for market data subscription
   */
  static async getWebSocketInstruments(filters?: {
    limit?: number;
    sector?: string;
    industry?: string;
    exchanges?: string[];
  }): Promise<WebSocketInstrument[]> {
    try {
      const instruments: WebSocketInstrument[] = [];

      // Search companies with filters
      const searchResult = await db.searchCompanies({
        sector: filters?.sector,
        industry: filters?.industry,
        limit: filters?.limit || 25000,
        offset: 0,
      });

      const companies: Company[] = searchResult.companies;

      let nseCount = 0;
      let bseCount = 0;

      for (const company of companies) {
        // Add NSE instrument if NSE_SECURITY_ID exists and is valid
        if (
          company.nse_security_id &&
          company.nse_security_id.trim() !== "" &&
          company.nse_security_id.trim() !== "-" &&
          company.nse_security_id.trim() !== "null" &&
          company.nse_security_id.trim() !== "NULL" &&
          (!filters?.exchanges || filters.exchanges.includes("NSE"))
        ) {
          instruments.push({
            ExchangeSegment: "NSE_EQ",
            SecurityId: company.nse_security_id.trim(),
            ...(company.nse_symbol && { symbol: company.nse_symbol }),
            companyName: company.company_name,
            ...(company.sector_name && { sector: company.sector_name }),
            ...(company.industry_name && { industry: company.industry_name }),
          });
          nseCount++;
        }

        // Add BSE instrument if BSE_SECURITY_ID exists and is valid
        if (
          company.bse_security_id &&
          company.bse_security_id.trim() !== "" &&
          company.bse_security_id.trim() !== "-" &&
          company.bse_security_id.trim() !== "null" &&
          company.bse_security_id.trim() !== "NULL" &&
          (!filters?.exchanges || filters.exchanges.includes("BSE"))
        ) {
          instruments.push({
            ExchangeSegment: "BSE_EQ",
            SecurityId: company.bse_security_id.trim(),
            ...(company.nse_symbol && { symbol: company.nse_symbol }), // Use NSE symbol as reference
            companyName: company.company_name,
            ...(company.sector_name && { sector: company.sector_name }),
            ...(company.industry_name && { industry: company.industry_name }),
          });
          bseCount++;
        }
      }

      console.log(
        `📊 Generated instruments: NSE=${nseCount}, BSE=${bseCount}, Total=${instruments.length}`
      );

      return instruments;
    } catch (error) {
      console.error("Error getting WebSocket instruments:", error);
      throw error;
    }
  }

  /**
   * Get instruments by sector
   */
  static async getInstrumentsBySector(
    sector: string,
    exchange: "NSE" | "BSE" = "NSE"
  ): Promise<WebSocketInstrument[]> {
    try {
      const companies = await db.getCompaniesBySector(sector);
      const instruments: WebSocketInstrument[] = [];

      for (const company of companies) {
        if (exchange === "NSE" && company.nse_security_id) {
          instruments.push({
            ExchangeSegment: "NSE_EQ",
            SecurityId: company.nse_security_id,
            symbol: company.nse_symbol || undefined,
            companyName: company.company_name,
            sector: company.sector_name || undefined,
            industry: company.industry_name || undefined,
          });
        } else if (exchange === "BSE" && company.bse_security_id) {
          instruments.push({
            ExchangeSegment: "BSE_EQ",
            SecurityId: company.bse_security_id,
            symbol: company.nse_symbol || undefined,
            companyName: company.company_name,
            sector: company.sector_name || undefined,
            industry: company.industry_name || undefined,
          });
        }
      }

      return instruments;
    } catch (error) {
      console.error("Error getting instruments by sector:", error);
      throw error;
    }
  }

  /**
   * Get instruments for specific index constituents
   */
  static async getInstrumentsForIndex(
    symbols: string[],
    exchange: "NSE" | "BSE" = "NSE"
  ): Promise<WebSocketInstrument[]> {
    try {
      const instruments: WebSocketInstrument[] = [];

      for (const symbol of symbols) {
        const company = await db.getCompanyBySymbol(symbol);

        if (company) {
          if (exchange === "NSE" && company.nse_security_id) {
            instruments.push({
              ExchangeSegment: "NSE_EQ",
              SecurityId: company.nse_security_id,
              symbol: company.nse_symbol || undefined,
              companyName: company.company_name,
              sector: company.sector_name || undefined,
              industry: company.industry_name || undefined,
            });
          } else if (exchange === "BSE" && company.bse_security_id) {
            instruments.push({
              ExchangeSegment: "BSE_EQ",
              SecurityId: company.bse_security_id,
              symbol: company.nse_symbol || undefined,
              companyName: company.company_name,
              sector: company.sector_name || undefined,
              industry: company.industry_name || undefined,
            });
          }
        } else {
          console.warn(`⚠️ Company not found for symbol: ${symbol}`);
        }
      }

      return instruments;
    } catch (error) {
      console.error("Error getting instruments for index:", error);
      throw error;
    }
  }

  /**
   * Get all available sectors
   */
  static async getAvailableSectors(): Promise<string[]> {
    try {
      const sectors = await db.getSectors();
      return sectors;
    } catch (error) {
      console.error("Error getting available sectors:", error);
      throw error;
    }
  }

  /**
   * Get instruments by multiple sectors
   */
  static async getInstrumentsByMultipleSectors(
    sectors: string[],
    exchange: "NSE" | "BSE" = "NSE"
  ): Promise<WebSocketInstrument[]> {
    try {
      const allInstruments: WebSocketInstrument[] = [];

      for (const sector of sectors) {
        const companies = await db.getCompaniesBySector(sector);

        for (const company of companies) {
          if (exchange === "NSE" && company.nse_security_id) {
            allInstruments.push({
              ExchangeSegment: "NSE_EQ",
              SecurityId: company.nse_security_id,
              symbol: company.nse_symbol || undefined,
              companyName: company.company_name,
              sector: company.sector_name || undefined,
              industry: company.industry_name || undefined,
            });
          } else if (exchange === "BSE" && company.bse_security_id) {
            allInstruments.push({
              ExchangeSegment: "BSE_EQ",
              SecurityId: company.bse_security_id,
              symbol: company.nse_symbol || undefined,
              companyName: company.company_name,
              sector: company.sector_name || undefined,
              industry: company.industry_name || undefined,
            });
          }
        }
      }

      return allInstruments;
    } catch (error) {
      console.error("Error getting instruments by multiple sectors:", error);
      throw error;
    }
  }

  /**
   * Split instruments into multiple connections (max 5000 per connection)
   */
  static splitIntoConnections(
    instruments: WebSocketInstrument[]
  ): WebSocketConnection[] {
    const connections: WebSocketConnection[] = [];

    for (
      let i = 0;
      i < instruments.length;
      i += this.MAX_INSTRUMENTS_PER_CONNECTION
    ) {
      const connectionInstruments = instruments.slice(
        i,
        i + this.MAX_INSTRUMENTS_PER_CONNECTION
      );
      const connectionId =
        Math.floor(i / this.MAX_INSTRUMENTS_PER_CONNECTION) + 1;

      if (connectionId > this.MAX_CONNECTIONS) {
        console.warn(
          `⚠️ Exceeded maximum connections (${this.MAX_CONNECTIONS}). Truncating instruments.`
        );
        break;
      }

      connections.push({
        connectionId,
        instruments: connectionInstruments,
        count: connectionInstruments.length,
      });
    }

    console.log(
      `📡 Split ${instruments.length} instruments into ${connections.length} connections`
    );
    connections.forEach((conn) => {
      console.log(
        `  Connection ${conn.connectionId}: ${conn.count} instruments`
      );
    });

    return connections;
  }

  /**
   * Get predefined index instruments (Bank Nifty, Nifty IT, etc.)
   */
  static async getPredefinedIndexInstruments(
    indexName: string
  ): Promise<WebSocketInstrument[]> {
    const indexSymbols: { [key: string]: string[] } = {
      BANKNIFTY: [
        "HDFCBANK",
        "ICICIBANK",
        "AXISBANK",
        "SBIN",
        "KOTAKBANK",
        "INDUSINDBK",
        "FEDERALBNK",
        "BANKBARODA",
        "IDFCFIRSTB",
        "AUBANK",
        "PNB",
      ],
      NIFTYIT: [
        "TCS",
        "INFY",
        "HCLTECH",
        "WIPRO",
        "TECHM",
        "LTIM",
        "PERSISTENT",
        "MPHASIS",
        "COFORGE",
        "LTTS",
      ],
      NIFTYAUTO: [
        "MARUTI",
        "M&M",
        "TATAMOTORS",
        "BAJAJ-AUTO",
        "HEROMOTOCO",
        "EICHERMOT",
        "TVSMOTOR",
        "BOSCHLTD",
        "ASHOKLEY",
        "BHARATFORG",
      ],
    };

    const symbols = indexSymbols[indexName.toUpperCase()];
    if (!symbols) {
      throw new Error(`Index ${indexName} not found`);
    }

    return await this.getInstrumentsForIndex(symbols);
  }

  /**
   * Generate subscription message for Dhan WebSocket
   */
  static generateSubscriptionMessage(
    instruments: WebSocketInstrument[],
    requestCode: number = 17 // 15=ticker, 17=quote, 21=full
  ) {
    return {
      RequestCode: requestCode,
      InstrumentCount: instruments.length,
      InstrumentList: instruments.map((instrument) => ({
        ExchangeSegment: instrument.ExchangeSegment,
        SecurityId: instrument.SecurityId,
      })),
    };
  }

  /**
   * Get sector-wise instrument distribution
   */
  static async getSectorDistribution(): Promise<
    {
      sector: string;
      nseInstruments: number;
      bseInstruments: number;
      totalInstruments: number;
    }[]
  > {
    try {
      const sectors = await db.getSectors();
      const distribution = [];

      for (const sector of sectors) {
        const companies = await db.getCompaniesBySector(sector);

        const nseCount = companies.filter((c) => c.nse_security_id).length;
        const bseCount = companies.filter((c) => c.bse_security_id).length;

        distribution.push({
          sector,
          nseInstruments: nseCount,
          bseInstruments: bseCount,
          totalInstruments: nseCount + bseCount,
        });
      }

      return distribution.sort(
        (a, b) => b.totalInstruments - a.totalInstruments
      );
    } catch (error) {
      console.error("Error getting sector distribution:", error);
      throw error;
    }
  }
}

export default WebSocketSubscriptionService;
