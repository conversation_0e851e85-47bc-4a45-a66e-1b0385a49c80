export interface Instrument {
    securityId: number;
    ticker: string;
    exchange: string;
    exchangeCode: number;
    segment: string;
    lotUnits: number;
}
export declare class WorkingDataService {
    private pool;
    private isConnected;
    constructor();
    /**
     * Load all instruments from database for WebSocket subscription
     */
    loadAllInstruments(maxInstruments?: number): Promise<Instrument[]>;
    /**
     * Get companies by exchange for instrument loading
     */
    getCompaniesByExchange(exchange: string, limit?: number, offset?: number): Promise<any[]>;
    /**
     * Search for a company by symbol
     */
    findCompanyBySymbol(symbol: string): Promise<any | null>;
    /**
     * Check database connection
     */
    checkConnection(): Promise<boolean>;
    /**
     * Get connection status
     */
    isConnectionHealthy(): boolean;
    /**
     * Close all connections
     */
    close(): Promise<void>;
}
export declare const workingDataService: WorkingDataService;
//# sourceMappingURL=WorkingDataService.d.ts.map