import { MarketData, Instrument } from "@/types";
export declare class MockDataGenerator {
    private baseValues;
    private lastValues;
    private volatility;
    constructor();
    /**
     * Initialize realistic base values for common indices
     */
    private initializeBaseValues;
    /**
     * Get base value for an instrument
     */
    private getBaseValue;
    /**
     * Get volatility for an instrument
     */
    private getVolatility;
    /**
     * Generate realistic market data for a single instrument
     */
    generateMarketData(instrument: Instrument): MarketData;
    /**
     * Generate market data for multiple instruments
     */
    generateBatchMarketData(instruments: Instrument[]): MarketData[];
    /**
     * Reset all values to base (simulate market open)
     */
    resetToBase(): void;
    /**
     * Simulate market volatility event (sudden price movements)
     */
    simulateVolatilityEvent(intensity?: number): void;
    /**
     * Get current statistics
     */
    getStats(): {
        totalInstruments: number;
        avgPrice: number;
        avgVolatility: number;
        priceRange: {
            min: number;
            max: number;
        };
    };
    /**
     * Update base value for an instrument (simulate corporate actions)
     */
    updateBaseValue(ticker: string, newBaseValue: number): void;
    /**
     * Set volatility for an instrument
     */
    setVolatility(ticker: string, volatility: number): void;
}
export declare const mockDataGenerator: MockDataGenerator;
export default MockDataGenerator;
//# sourceMappingURL=MockDataGenerator.d.ts.map