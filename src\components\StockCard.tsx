"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { TrendingUp, TrendingDown, Volume2, Clock } from "lucide-react";

interface StockData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
}

interface CompanyInfo {
  company_name: string;
  nse_symbol?: string;
  bse_symbol?: string;
  nse_security_id?: string;
  bse_security_id?: string;
  sector_name?: string;
  industry_name?: string;
}

interface StockCardProps {
  symbol: string;
  companyInfo?: CompanyInfo;
  className?: string;
}

export default function StockCard({
  symbol,
  companyInfo,
  className = "",
}: StockCardProps) {
  const [selectedExchange, setSelectedExchange] = useState<"NSE" | "BSE">(
    "NSE"
  );
  const [stockData, setStockData] = useState<StockData | null>(null);
  const [loading, setLoading] = useState(true);
  const [availableExchanges, setAvailableExchanges] = useState<
    ("NSE" | "BSE")[]
  >([]);

  // Determine available exchanges for this stock
  useEffect(() => {
    if (companyInfo) {
      const exchanges: ("NSE" | "BSE")[] = [];

      if (
        companyInfo.nse_security_id &&
        companyInfo.nse_security_id !== "-" &&
        companyInfo.nse_security_id !== "null"
      ) {
        exchanges.push("NSE");
      }

      if (
        companyInfo.bse_security_id &&
        companyInfo.bse_security_id !== "-" &&
        companyInfo.bse_security_id !== "null"
      ) {
        exchanges.push("BSE");
      }

      setAvailableExchanges(exchanges);

      // Set default exchange to the first available
      if (exchanges.length > 0 && !exchanges.includes(selectedExchange)) {
        setSelectedExchange(exchanges[0]);
      }
    }
  }, [companyInfo, selectedExchange]);

  // Fetch stock data based on selected exchange
  useEffect(() => {
    const fetchStockData = async () => {
      if (!companyInfo || availableExchanges.length === 0) return;

      try {
        setLoading(true);

        // Get the security ID for the selected exchange
        const securityId =
          selectedExchange === "NSE"
            ? companyInfo.nse_security_id
            : companyInfo.bse_security_id;

        if (!securityId || securityId === "-" || securityId === "null") {
          setStockData(null);
          return;
        }

        const response = await fetch(`/api/instrument/${securityId}`);

        if (response.ok) {
          const data = await response.json();
          setStockData(data);
        } else {
          setStockData(null);
        }
      } catch (error) {
        console.error("Error fetching stock data:", error);
        setStockData(null);
      } finally {
        setLoading(false);
      }
    };

    fetchStockData();

    // Set up real-time updates
    const interval = setInterval(fetchStockData, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [selectedExchange, companyInfo, availableExchanges]);

  const formatPrice = (price: number | undefined | null) => {
    if (price == null || isNaN(price)) return "₹0.00";
    return `₹${price.toFixed(2)}`;
  };

  const formatChange = (
    change: number | undefined | null,
    changePercent: number | undefined | null
  ) => {
    if (
      change == null ||
      changePercent == null ||
      isNaN(change) ||
      isNaN(changePercent)
    ) {
      return "0.00 (0.00%)";
    }
    const sign = change >= 0 ? "+" : "";
    return `${sign}${change.toFixed(2)} (${sign}${changePercent.toFixed(2)}%)`;
  };

  const formatVolume = (volume: number | undefined | null) => {
    if (volume == null || isNaN(volume)) return "0";
    if (volume >= 10000000) return `${(volume / 10000000).toFixed(1)}Cr`;
    if (volume >= 100000) return `${(volume / 100000).toFixed(1)}L`;
    if (volume >= 1000) return `${(volume / 1000).toFixed(1)}K`;
    return volume.toString();
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString("en-IN", {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  };

  if (loading) {
    return (
      <Card className={`animate-pulse ${className}`}>
        <CardContent className="p-6">
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!stockData) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <p>No data available for {symbol}</p>
            {availableExchanges.length === 0 && (
              <p className="text-sm">Not listed on any exchange</p>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`hover:shadow-lg transition-shadow ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-xl font-bold">{symbol}</CardTitle>
            <p className="text-sm text-muted-foreground mt-1">
              {companyInfo?.company_name || "Unknown Company"}
            </p>
            {companyInfo?.sector_name && (
              <Badge variant="outline" className="mt-2 text-xs">
                {companyInfo.sector_name}
              </Badge>
            )}
          </div>

          {/* Exchange Toggle - only show if multiple exchanges available */}
          {availableExchanges.length > 1 && (
            <div className="flex rounded-lg border">
              {availableExchanges.map((exchange) => (
                <Button
                  key={exchange}
                  variant={selectedExchange === exchange ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setSelectedExchange(exchange)}
                  className={`text-xs px-3 py-1 ${
                    exchange === "NSE" ? "rounded-r-none" : "rounded-l-none"
                  }`}
                >
                  {exchange}
                </Button>
              ))}
            </div>
          )}

          {/* Single exchange badge */}
          {availableExchanges.length === 1 && (
            <Badge variant="secondary" className="text-xs">
              {availableExchanges[0]}
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Price and Change */}
        <div className="space-y-2">
          <div className="flex items-baseline gap-2">
            <span className="text-3xl font-bold">
              {formatPrice(stockData.ltp)}
            </span>
            <div
              className={`flex items-center gap-1 ${
                stockData.change >= 0 ? "text-green-600" : "text-red-600"
              }`}
            >
              {stockData.change >= 0 ? (
                <TrendingUp className="h-4 w-4" />
              ) : (
                <TrendingDown className="h-4 w-4" />
              )}
              <span className="font-medium">
                {formatChange(stockData.change, stockData.changePercent)}
              </span>
            </div>
          </div>
        </div>

        {/* OHLC Data */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-1">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Open:</span>
              <span className="font-medium">{formatPrice(stockData.open)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">High:</span>
              <span className="font-medium text-green-600">
                {formatPrice(stockData.high)}
              </span>
            </div>
          </div>

          <div className="space-y-1">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Close:</span>
              <span className="font-medium">
                {formatPrice(stockData.close)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Low:</span>
              <span className="font-medium text-red-600">
                {formatPrice(stockData.low)}
              </span>
            </div>
          </div>
        </div>

        {/* Volume and Last Update */}
        <div className="flex items-center justify-between pt-2 border-t">
          <div className="flex items-center gap-1 text-sm text-muted-foreground">
            <Volume2 className="h-4 w-4" />
            <span>Vol: {formatVolume(stockData.volume)}</span>
          </div>

          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            <span>{formatTime(stockData.timestamp)}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
