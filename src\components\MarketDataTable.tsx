import React, { memo, useMemo } from "react";

interface MarketData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
}

interface MarketDataTableProps {
  data: MarketData[];
}

// Memoized row component for better performance
const MarketDataRow = memo(({ item }: { item: MarketData }) => {
  const formatNumber = useMemo(() => {
    return (num: number) =>
      num.toLocaleString(undefined, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
  }, []);

  const formatVolume = useMemo(() => {
    return (num: number) => num.toLocaleString();
  }, []);

  const formatTime = useMemo(() => {
    return (timestamp: number) => new Date(timestamp).toLocaleTimeString();
  }, []);

  const getPriceClass = useMemo(() => {
    return (change: number) => {
      if (change > 0) return "text-green-600";
      if (change < 0) return "text-red-600";
      return "text-gray-900";
    };
  }, []);

  const getRowClass = useMemo(() => {
    return (change: number) => {
      if (change > 0) return "hover:bg-green-50 bg-green-25";
      if (change < 0) return "hover:bg-red-50 bg-red-25";
      return "hover:bg-gray-50";
    };
  }, []);

  return (
    <tr key={item.securityId} className={getRowClass(item.change)}>
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
        {item.ticker}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {item.securityId}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        <span className="px-2 py-1 text-xs font-medium bg-gray-100 rounded-full">
          {item.exchange}
        </span>
      </td>
      <td
        className={`px-6 py-4 whitespace-nowrap text-sm text-right font-medium ${getPriceClass(
          item.change
        )}`}
      >
        ₹{formatNumber(item.ltp)}
      </td>
      <td
        className={`px-6 py-4 whitespace-nowrap text-sm text-right font-medium ${getPriceClass(
          item.change
        )}`}
      >
        {item.change >= 0 ? "+" : ""}
        {formatNumber(item.change)}
      </td>
      <td
        className={`px-6 py-4 whitespace-nowrap text-sm text-right font-medium ${getPriceClass(
          item.change
        )}`}
      >
        {item.changePercent >= 0 ? "+" : ""}
        {formatNumber(item.changePercent)}%
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500">
        {formatVolume(item.volume)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500">
        ₹{formatNumber(item.high)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500">
        ₹{formatNumber(item.low)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500">
        ₹{formatNumber(item.open)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500">
        ₹{formatNumber(item.close)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500">
        {formatTime(item.timestamp)}
      </td>
    </tr>
  );
});

MarketDataRow.displayName = "MarketDataRow";

function MarketDataTable({ data }: MarketDataTableProps) {
  // Memoize the rendered rows for better performance
  const renderedRows = useMemo(() => {
    return data.map((item) => (
      <MarketDataRow key={item.securityId} item={item} />
    ));
  }, [data]);

  if (data.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 text-lg">No market data available</div>
        <div className="text-gray-400 text-sm mt-2">
          Waiting for real-time updates...
        </div>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto max-h-96 overflow-y-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50 sticky top-0 z-10">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Ticker
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Security ID
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Exchange
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              LTP
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Change
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Change %
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Volume
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              High
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Low
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Open
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Close
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Updated
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {renderedRows}
        </tbody>
      </table>
    </div>
  );
}

export default memo(MarketDataTable);
