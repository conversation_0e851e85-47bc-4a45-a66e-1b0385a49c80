declare class Logger {
    private static instance;
    private logStream;
    private logQueue;
    private isProcessing;
    private constructor();
    static getInstance(): Logger;
    private initializeLogStream;
    private getLogLevelValue;
    private shouldLog;
    private formatLogEntry;
    private processLogQueue;
    private log;
    error(message: string, data?: any): void;
    warn(message: string, data?: any): void;
    info(message: string, data?: any): void;
    debug(message: string, data?: any): void;
    logRequest(req: any, res: any, next: any): void;
    logWebSocketEvent(event: string, data?: any): void;
    logDatabaseOperation(operation: string, data?: any): void;
    close(): void;
}
export default Logger;
//# sourceMappingURL=logger.d.ts.map