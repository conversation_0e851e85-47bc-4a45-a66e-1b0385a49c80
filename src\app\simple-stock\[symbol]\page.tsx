"use client";

import React, { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowLeft, TrendingUp, TrendingDown } from "lucide-react";

interface StockData {
  ticker: string;
  securityId: string;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
}

interface CompanyInfo {
  company_name: string;
  symbol?: string;
  nse_security_id?: string;
  bse_security_id?: string;
  sector_name?: string;
  industry_new_name?: string;
  sub_sector?: string;
  micro_category?: string;
}

export default function SimpleStockPage() {
  const params = useParams();
  const symbol = params.symbol as string;

  const [stockData, setStockData] = useState<StockData | null>(null);
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStockData = async () => {
      try {
        // Fetch company info
        const companyRes = await fetch(
          `/api/companies?symbol=${symbol}&limit=1`
        );
        if (companyRes.ok) {
          const companyData = await companyRes.json();
          if (companyData.success && companyData.data.length > 0) {
            setCompanyInfo(companyData.data[0]);
          }
        }

        // Fetch market data
        const marketRes = await fetch("/api/data?exchange=NSE_EQ&limit=5000");
        if (marketRes.ok) {
          const marketData = await marketRes.json();
          if (marketData.latestData) {
            const stock = marketData.latestData.find(
              (s: any) => s.ticker === symbol
            );
            if (stock) {
              setStockData({
                ticker: stock.ticker,
                securityId: stock.securityId,
                ltp: stock.ltp || 0,
                change: stock.change || 0,
                changePercent: stock.changePercent || 0,
                volume: stock.volume || 0,
                high: stock.high || 0,
                low: stock.low || 0,
                open: stock.open || 0,
                close: stock.close || 0,
              });
            }
          }
        }
      } catch (error) {
        console.error("Error fetching stock data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchStockData();

    // Refresh every 5 seconds
    const interval = setInterval(fetchStockData, 5000);
    return () => clearInterval(interval);
  }, [symbol]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-xl">Loading {symbol} data...</div>
      </div>
    );
  }

  if (!stockData) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-4 mb-6">
          <Link href="/simple-dashboard">
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </Link>
        </div>
        <Card>
          <CardContent className="p-8 text-center">
            <h2 className="text-xl font-semibold mb-2">Stock Not Found</h2>
            <p className="text-gray-600">No live data available for {symbol}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const isPositive = stockData.change >= 0;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/simple-dashboard">
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">{symbol}</h1>
          {companyInfo && (
            <p className="text-gray-600">{companyInfo.company_name}</p>
          )}
        </div>
      </div>

      {/* Price Card */}
      <Card>
        <CardContent className="p-8">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-4xl font-bold">
                ₹{stockData.ltp.toFixed(2)}
              </div>
              <div
                className={`flex items-center gap-2 text-lg ${isPositive ? "text-green-600" : "text-red-600"}`}
              >
                {isPositive ? (
                  <TrendingUp className="h-5 w-5" />
                ) : (
                  <TrendingDown className="h-5 w-5" />
                )}
                {isPositive ? "+" : ""}
                {stockData.change.toFixed(2)} (
                {stockData.changePercent.toFixed(2)}%)
              </div>
            </div>
            <div className="text-right text-sm text-gray-600">
              <div>Volume: {stockData.volume.toLocaleString()}</div>
              <div>Security ID: {stockData.securityId}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Market Data */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-sm text-gray-600">Open</div>
            <div className="text-xl font-semibold">
              ₹{stockData.open.toFixed(2)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-sm text-gray-600">High</div>
            <div className="text-xl font-semibold text-green-600">
              ₹{stockData.high.toFixed(2)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-sm text-gray-600">Low</div>
            <div className="text-xl font-semibold text-red-600">
              ₹{stockData.low.toFixed(2)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-sm text-gray-600">Previous Close</div>
            <div className="text-xl font-semibold">
              ₹{stockData.close.toFixed(2)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Company Info */}
      {companyInfo && (
        <Card>
          <CardHeader>
            <CardTitle>Company Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-gray-600">Sector</div>
                <div className="font-semibold">
                  {companyInfo.sector_name || "N/A"}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Industry</div>
                <div className="font-semibold">
                  {companyInfo.industry_new_name || "N/A"}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Sub-Sector</div>
                <div className="font-semibold">
                  {companyInfo.sub_sector || "N/A"}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Micro-Category</div>
                <div className="font-semibold">
                  {companyInfo.micro_category || "N/A"}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
