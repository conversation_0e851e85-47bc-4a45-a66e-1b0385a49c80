const { Client } = require('pg');
require('dotenv').config();

console.log('🚀 Running database migration...');

// Use the working connection from environment
const client = new Client({
  connectionString: process.env.POSTGRES_DATABASE_URL,
  ssl: { rejectUnauthorized: false },
  connectionTimeoutMillis: 15000
});

// Migration: Create company_list table
const createCompanyListTable = `
  CREATE TABLE IF NOT EXISTS company_list (
    id SERIAL PRIMARY KEY,
    company_name VARCHAR(500) NOT NULL,
    isin_no VARCHAR(20),
    instrument VARCHAR(50),
    sector_name VARCHAR(200),
    industry_new_name VARCHAR(200),
    sub_sector VARCHAR(200),
    micro_category VARCHAR(200),
    bse_security_id VARCHAR(20),
    nse_security_id VARCHAR(20),
    symbol VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );
`;

// Migration: Create indexes for better performance
const createIndexes = [
  'CREATE INDEX IF NOT EXISTS idx_company_list_symbol ON company_list(symbol);',
  'CREATE INDEX IF NOT EXISTS idx_company_list_sector ON company_list(sector_name);',
  'CREATE INDEX IF NOT EXISTS idx_company_list_industry ON company_list(industry_new_name);',
  'CREATE INDEX IF NOT EXISTS idx_company_list_nse_id ON company_list(nse_security_id);',
  'CREATE INDEX IF NOT EXISTS idx_company_list_bse_id ON company_list(bse_security_id);',
  'CREATE INDEX IF NOT EXISTS idx_company_list_isin ON company_list(isin_no);'
];

// Migration: Create watchlist tables
const createWatchlistTables = [
  `CREATE TABLE IF NOT EXISTS watchlist_sectors (
    id SERIAL PRIMARY KEY,
    sector_name VARCHAR(200) NOT NULL UNIQUE,
    company_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );`,
  
  `CREATE TABLE IF NOT EXISTS watchlist_industries (
    id SERIAL PRIMARY KEY,
    industry_name VARCHAR(200) NOT NULL UNIQUE,
    sector_name VARCHAR(200),
    company_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );`,
  
  `CREATE TABLE IF NOT EXISTS user_watchlists (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(100),
    watchlist_name VARCHAR(200) NOT NULL,
    watchlist_type VARCHAR(50) DEFAULT 'custom',
    companies JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );`
];

async function runMigration() {
  try {
    console.log('🔗 Connecting to Supabase database...');
    await client.connect();
    console.log('✅ Connected to database');

    // Create main company_list table
    console.log('🏗️ Creating company_list table...');
    await client.query(createCompanyListTable);
    console.log('✅ Company_list table created/verified');

    // Create indexes
    console.log('🔍 Creating database indexes...');
    for (const indexSQL of createIndexes) {
      try {
        await client.query(indexSQL);
        console.log(`✅ Index created: ${indexSQL.split(' ')[5]}`);
      } catch (error) {
        console.log(`⚠️ Index might already exist: ${indexSQL.split(' ')[5]}`);
      }
    }

    // Create watchlist tables
    console.log('📋 Creating watchlist tables...');
    for (const tableSQL of createWatchlistTables) {
      try {
        await client.query(tableSQL);
        const tableName = tableSQL.match(/CREATE TABLE IF NOT EXISTS (\w+)/)[1];
        console.log(`✅ Table created: ${tableName}`);
      } catch (error) {
        console.log(`⚠️ Table creation error: ${error.message}`);
      }
    }

    // Check current data
    console.log('📊 Checking current data...');
    try {
      const countResult = await client.query('SELECT COUNT(*) as count FROM company_list');
      console.log(`📈 Current records in company_list: ${countResult.rows[0].count}`);
      
      if (countResult.rows[0].count > 0) {
        // Show sample data
        const sampleResult = await client.query('SELECT company_name, symbol, sector_name, nse_security_id, bse_security_id FROM company_list LIMIT 3');
        console.log('\n📋 Sample data:');
        sampleResult.rows.forEach((row, index) => {
          console.log(`${index + 1}. ${row.company_name} (${row.symbol}) - ${row.sector_name}`);
          console.log(`   NSE: ${row.nse_security_id || 'N/A'}, BSE: ${row.bse_security_id || 'N/A'}`);
        });
      }
    } catch (error) {
      console.log('📊 Company_list table is empty or doesn\'t exist yet');
    }

    console.log('\n🎉 Database migration completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('1. Run: node import-to-supabase-working.js (to import company data)');
    console.log('2. Update your application to use the new database schema');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  } finally {
    try {
      await client.end();
      console.log('🔌 Database connection closed');
    } catch (e) {
      // Ignore cleanup errors
    }
  }
}

// Run migration
runMigration();
