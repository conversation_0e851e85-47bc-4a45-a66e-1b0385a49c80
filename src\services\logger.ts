import { appConfig } from "../config";
import { createWriteStream, WriteStream } from "fs";
import { join } from "path";

type LogLevel = "error" | "warn" | "info" | "debug";

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  data?: any;
}

class Logger {
  private static instance: Logger;
  private logStream: WriteStream | null = null;
  private logQueue: LogEntry[] = [];
  private isProcessing = false;

  private constructor() {
    this.initializeLogStream();
  }

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private initializeLogStream(): void {
    try {
      const logDir = join(process.cwd(), "logs");
      const logFile = join(
        logDir,
        `app-${new Date().toISOString().split("T")[0]}.log`
      );
      this.logStream = createWriteStream(logFile, { flags: "a" });
    } catch (error) {
      console.error("Failed to initialize log stream:", error);
    }
  }

  private getLogLevelValue(level: LogLevel): number {
    const levels: Record<LogLevel, number> = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3,
    };
    return levels[level];
  }

  private shouldLog(level: LogLevel): boolean {
    const configLevel = appConfig.logging.level;
    return (
      this.getLogLevelValue(level) <=
      this.getLogLevelValue(configLevel as LogLevel)
    );
  }

  private formatLogEntry(entry: LogEntry): string {
    const { timestamp, level, message, data } = entry;
    const dataStr = data ? `\n${JSON.stringify(data, null, 2)}` : "";
    return `[${timestamp}] ${level.toUpperCase()}: ${message}${dataStr}\n`;
  }

  private async processLogQueue(): Promise<void> {
    if (this.isProcessing || this.logQueue.length === 0) return;

    this.isProcessing = true;

    try {
      while (this.logQueue.length > 0) {
        const entry = this.logQueue.shift();
        if (entry) {
          const logMessage = this.formatLogEntry(entry);

          // Write to file if stream is available
          if (this.logStream) {
            this.logStream.write(logMessage);
          }

          // Also log to console
          switch (entry.level) {
            case "error":
              console.error(logMessage);
              break;
            case "warn":
              console.warn(logMessage);
              break;
            case "info":
              console.info(logMessage);
              break;
            case "debug":
              console.debug(logMessage);
              break;
          }
        }
      }
    } catch (error) {
      console.error("Error processing log queue:", error);
    } finally {
      this.isProcessing = false;
    }
  }

  private log(level: LogLevel, message: string, data?: any): void {
    if (!this.shouldLog(level)) return;

    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      data,
    };

    this.logQueue.push(entry);
    this.processLogQueue();
  }

  error(message: string, data?: any): void {
    this.log("error", message, data);
  }

  warn(message: string, data?: any): void {
    this.log("warn", message, data);
  }

  info(message: string, data?: any): void {
    this.log("info", message, data);
  }

  debug(message: string, data?: any): void {
    this.log("debug", message, data);
  }

  // Log HTTP request
  logRequest(req: any, res: any, next: any): void {
    const start = Date.now();
    res.on("finish", () => {
      const duration = Date.now() - start;
      this.info("HTTP Request", {
        method: req.method,
        url: req.originalUrl,
        status: res.statusCode,
        duration: `${duration}ms`,
        ip: req.ip,
        userAgent: req.get("user-agent"),
      });
    });
    next();
  }

  // Log WebSocket events
  logWebSocketEvent(event: string, data?: any): void {
    this.debug("WebSocket Event", { event, data });
  }

  // Log database operations
  logDatabaseOperation(operation: string, data?: any): void {
    this.debug("Database Operation", { operation, data });
  }

  // Close log stream
  close(): void {
    if (this.logStream) {
      this.logStream.end();
      this.logStream = null;
    }
  }
}

export default Logger;
