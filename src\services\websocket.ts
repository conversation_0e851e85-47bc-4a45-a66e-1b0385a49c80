import WebSocket from "ws";

interface WebSocketMessage {
  type: string;
  data: any;
}

class WebSocketService {
  private static instance: WebSocketService;
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private messageQueue: WebSocketMessage[] = [];
  private isConnected = false;
  private messageHandlers: Map<string, ((data: any) => void)[]> = new Map();

  private constructor() {
    // Private constructor for singleton pattern
  }

  static getInstance(): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService();
    }
    return WebSocketService.instance;
  }

  // Connect to WebSocket server
  async connect(url: string): Promise<void> {
    if (this.isConnected) return;

    try {
      this.ws = new WebSocket(url);

      this.ws.on("open", () => {
        console.log("✅ WebSocket connection established");
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.startHeartbeat();
        this.processMessageQueue();
      });

      this.ws.on("message", (data: WebSocket.Data) => {
        try {
          const message = JSON.parse(data.toString()) as WebSocketMessage;
          this.handleMessage(message);
        } catch (error) {
          console.error("❌ Error parsing WebSocket message:", error);
        }
      });

      this.ws.on("close", () => {
        console.log("🔌 WebSocket connection closed");
        this.handleDisconnect();
      });

      this.ws.on("error", (error) => {
        console.error("❌ WebSocket error:", error);
        this.handleError(error);
      });

      // Wait for connection to be established
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(
          () => {
            reject(new Error("WebSocket connection timeout"));
          },
          parseInt(process.env.WEBSOCKET_CONNECTION_TIMEOUT || "30000")
        );

        this.ws!.once("open", () => {
          clearTimeout(timeout);
          resolve();
        });
      });
    } catch (error) {
      console.error("❌ Failed to establish WebSocket connection:", error);
      throw new Error("Failed to establish WebSocket connection");
    }
  }

  // Send message to WebSocket server
  send(message: WebSocketMessage): void {
    if (!this.isConnected) {
      this.messageQueue.push(message);
      return;
    }

    try {
      this.ws!.send(JSON.stringify(message));
    } catch (error) {
      console.error("❌ Error sending WebSocket message:", error);
      this.messageQueue.push(message);
    }
  }

  // Subscribe to message type
  subscribe(type: string, handler: (data: any) => void): void {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, []);
    }
    this.messageHandlers.get(type)!.push(handler);
  }

  // Unsubscribe from message type
  unsubscribe(type: string, handler: (data: any) => void): void {
    const handlers = this.messageHandlers.get(type);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index !== -1) {
        handlers.splice(index, 1);
      }
    }
  }

  // Handle incoming messages
  private handleMessage(message: WebSocketMessage): void {
    const handlers = this.messageHandlers.get(message.type);
    if (handlers) {
      handlers.forEach((handler) => {
        try {
          handler(message.data);
        } catch (error) {
          console.error(
            `❌ Error in message handler for ${message.type}:`,
            error
          );
        }
      });
    }
  }

  // Handle disconnection
  private handleDisconnect(): void {
    this.isConnected = false;
    this.stopHeartbeat();

    const maxReconnectAttempts = parseInt(
      process.env.WEBSOCKET_RECONNECT_ATTEMPTS || "5"
    );

    if (this.reconnectAttempts < maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(
        `🔄 Attempting to reconnect (${this.reconnectAttempts}/${maxReconnectAttempts})...`
      );
      setTimeout(
        () => this.connect(process.env.WEBSOCKET_URL || ""),
        parseInt(process.env.WEBSOCKET_RECONNECT_DELAY || "5000")
      );
    } else {
      console.error("❌ Max reconnection attempts reached");
      throw new Error("Max reconnection attempts reached");
    }
  }

  // Handle errors
  private handleError(error: Error): void {
    console.error("❌ WebSocket error:", error);
    this.handleDisconnect();
  }

  // Start heartbeat
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(
      () => {
        if (this.isConnected) {
          this.send({ type: "ping", data: {} });
        }
      },
      parseInt(process.env.WEBSOCKET_HEARTBEAT_INTERVAL || "30000")
    );
  }

  // Stop heartbeat
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  // Process message queue
  private processMessageQueue(): void {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      if (message) {
        this.send(message);
      }
    }
  }

  // Close WebSocket connection
  close(): void {
    if (this.ws) {
      this.stopHeartbeat();
      this.ws.close();
      this.ws = null;
      this.isConnected = false;
      this.messageQueue = [];
      this.messageHandlers.clear();
    }
  }

  // Check if connected
  isConnectedToServer(): boolean {
    return this.isConnected;
  }
}

export default WebSocketService;
