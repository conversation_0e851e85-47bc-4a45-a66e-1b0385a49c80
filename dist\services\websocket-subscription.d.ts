export interface WebSocketInstrument {
    ExchangeSegment: string;
    SecurityId: string;
    symbol?: string;
    companyName?: string;
    sector?: string;
    industry?: string;
}
export interface WebSocketConnection {
    connectionId: number;
    instruments: WebSocketInstrument[];
    count: number;
}
export interface Company {
    id: number;
    company_name: string;
    nse_symbol?: string;
    bse_symbol?: string;
    nse_security_id?: string;
    bse_security_id?: string;
    sector_name?: string;
    industry_name?: string;
}
export declare class WebSocketSubscriptionService {
    private static readonly MAX_CONNECTIONS;
    private static readonly MAX_INSTRUMENTS_PER_CONNECTION;
    /**
     * Get WebSocket instruments for market data subscription
     */
    static getWebSocketInstruments(filters?: {
        limit?: number;
        sector?: string;
        industry?: string;
        exchanges?: string[];
    }): Promise<WebSocketInstrument[]>;
    /**
     * Get instruments by sector
     */
    static getInstrumentsBySector(sector: string, exchange?: "NSE" | "BSE"): Promise<WebSocketInstrument[]>;
    /**
     * Get instruments for specific index constituents
     */
    static getInstrumentsForIndex(symbols: string[], exchange?: "NSE" | "BSE"): Promise<WebSocketInstrument[]>;
    /**
     * Get all available sectors
     */
    static getAvailableSectors(): Promise<string[]>;
    /**
     * Get instruments by multiple sectors
     */
    static getInstrumentsByMultipleSectors(sectors: string[], exchange?: "NSE" | "BSE"): Promise<WebSocketInstrument[]>;
    /**
     * Split instruments into multiple connections (max 5000 per connection)
     */
    static splitIntoConnections(instruments: WebSocketInstrument[]): WebSocketConnection[];
    /**
     * Get predefined index instruments (Bank Nifty, Nifty IT, etc.)
     */
    static getPredefinedIndexInstruments(indexName: string): Promise<WebSocketInstrument[]>;
    /**
     * Generate subscription message for Dhan WebSocket
     */
    static generateSubscriptionMessage(instruments: WebSocketInstrument[], requestCode?: number): {
        RequestCode: number;
        InstrumentCount: number;
        InstrumentList: {
            ExchangeSegment: string;
            SecurityId: string;
        }[];
    };
    /**
     * Get sector-wise instrument distribution
     */
    static getSectorDistribution(): Promise<{
        sector: string;
        nseInstruments: number;
        bseInstruments: number;
        totalInstruments: number;
    }[]>;
}
export default WebSocketSubscriptionService;
//# sourceMappingURL=websocket-subscription.d.ts.map