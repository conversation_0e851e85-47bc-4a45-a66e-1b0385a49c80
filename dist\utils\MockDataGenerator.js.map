{"version": 3, "file": "MockDataGenerator.js", "sourceRoot": "", "sources": ["../../src/utils/MockDataGenerator.ts"], "names": [], "mappings": ";AAAA,+EAA+E;AAC/E,yDAAyD;AACzD,+EAA+E;;;AAI/E,MAAa,iBAAiB;IAK5B;QAJQ,eAAU,GAAwB,IAAI,GAAG,EAAE,CAAC;QAC5C,eAAU,GAAwB,IAAI,GAAG,EAAE,CAAC;QAC5C,eAAU,GAAwB,IAAI,GAAG,EAAE,CAAC;QAGlD,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,MAAM,eAAe,GAAG;YACtB,2CAA2C;YAC3C,MAAM,EAAE,KAAK;YACb,MAAM,EAAE,KAAK;YACb,MAAM,EAAE,KAAK;YACb,MAAM,EAAE,KAAK;YACb,MAAM,EAAE,KAAK;YACb,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,KAAK;YACX,EAAE,EAAE,KAAK;YACT,IAAI,EAAE,KAAK;YACX,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,IAAI;YACZ,EAAE,EAAE,KAAK;YACT,KAAK,EAAE,IAAI;YACX,GAAG,EAAE,KAAK;YACV,MAAM,EAAE,KAAK;YACb,QAAQ,EAAE,KAAK;YAEf,8BAA8B;YAC9B,OAAO,EAAE,KAAK;SACf,CAAC;QAEF,MAAM,aAAa,GAAG;YACpB,MAAM,EAAE,GAAG;YACX,MAAM,EAAE,GAAG;YACX,MAAM,EAAE,GAAG;YACX,MAAM,EAAE,GAAG;YACX,MAAM,EAAE,GAAG;YACX,MAAM,EAAE,GAAG;YACX,IAAI,EAAE,GAAG;YACT,EAAE,EAAE,GAAG;YACP,IAAI,EAAE,GAAG;YACT,KAAK,EAAE,GAAG;YACV,MAAM,EAAE,GAAG;YACX,EAAE,EAAE,GAAG;YACP,KAAK,EAAE,GAAG;YACV,GAAG,EAAE,GAAG;YACR,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE,GAAG;YACb,OAAO,EAAE,GAAG;SACb,CAAC;QAEF,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;YACvD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YAChC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YAChC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,EAAG,aAAqB,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,MAAc;QACjC,wBAAwB;QACxB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC;QACtC,CAAC;QAED,0CAA0C;QAC1C,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;QACzC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;YACjE,IAAI,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC3D,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,iDAAiD;QACjD,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;QAClE,CAAC;QACD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC,kBAAkB;QAEnE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QACvC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QACvC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAEjC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,MAAc;QAClC,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,UAAsB;QACvC,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACvD,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC;QACtE,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAE/D,oCAAoC;QACpC,MAAM,gBAAgB,GAAG,GAAG,GAAG,gBAAgB,CAAC,CAAC,mCAAmC;QACpF,MAAM,aAAa,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC,CAAC;QACnE,MAAM,MAAM,GAAG,CAAC,SAAS,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC;QACjD,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,GAAG,EAAE,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,6BAA6B;QAE3F,kDAAkD;QAClD,MAAM,WAAW,GAAG,MAAM,GAAG,SAAS,CAAC;QACvC,MAAM,kBAAkB,GAAG,CAAC,WAAW,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC;QAE3D,qBAAqB;QACrB,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,GAAG,KAAK,CAAC;QAC1E,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,GAAG,KAAK,CAAC;QACzE,MAAM,IAAI,GAAG,SAAS,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC;QAEpE,oDAAoD;QACpD,MAAM,UAAU,GAAG,MAAM,CAAC;QAC1B,MAAM,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC;QAChE,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CACvB,UAAU,GAAG,gBAAgB,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CACtD,CAAC;QAEF,uCAAuC;QACvC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAE/C,MAAM,UAAU,GAAe;YAC7B,MAAM,EAAE,UAAU,CAAC,MAAM;YACzB,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE;YAC5C,QAAQ,EAAE,UAAU,CAAC,QAAQ;YAC7B,YAAY,EAAE,UAAU,CAAC,YAAY;YACrC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG;YACnC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,GAAG;YAC3C,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,GAAG,CAAC,GAAG,GAAG;YACzD,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG;YAClC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;YAChC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG;YAClC,KAAK,EAAE,SAAS,EAAE,uBAAuB;YACzC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC;QAEF,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,uBAAuB,CAAC,WAAyB;QAC/C,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,WAAW;QACT,KAAK,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;YACxE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,uBAAuB,CAAC,YAAoB,GAAG;QAC7C,KAAK,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;YACxE,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,SAAS,GAAG,SAAS,GAAG,IAAI,GAAG,SAAS,GAAG,UAAU,CAAC,CAAC,kBAAkB;YAC/E,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,CAAC;YACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,GAAG,EAAE,SAAS,GAAG,MAAM,CAAC,CAAC;YAC/D,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,QAAQ;QAMN,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;QACpD,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;QAE1D,OAAO;YACL,gBAAgB,EAAE,MAAM,CAAC,MAAM;YAC/B,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM;YACnE,aAAa,EACX,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,YAAY,CAAC,MAAM;YACvE,UAAU,EAAE;gBACV,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;gBACxB,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;aACzB;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,MAAc,EAAE,YAAoB;QAClD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAC1C,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAAc,EAAE,UAAkB;QAC9C,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;IACxE,CAAC;CACF;AA5ND,8CA4NC;AAED,qBAAqB;AACR,QAAA,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC;AAEzD,kBAAe,iBAAiB,CAAC"}