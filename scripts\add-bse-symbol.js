import { Client } from "pg";
import dotenv from "dotenv";
import { fileURLToPath } from "url";
import { dirname } from "path";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

dotenv.config();

// Database connection
const client = new Client({
  connectionString: process.env.POSTGRES_DATABASE_URL,
  ssl: {
    rejectUnauthorized: false, // Accept self-signed certificates
  },
});

async function addBseSymbolColumn() {
  try {
    await client.connect();
    console.log("Connected to database");

    // Add bse_symbol column if it doesn't exist
    const addColumnSQL = `
      ALTER TABLE companies
      ADD COLUMN IF NOT EXISTS bse_symbol VARCHAR(50);
    `;
    await client.query(addColumnSQL);
    console.log("Added bse_symbol column");

    // Create index for better performance
    const createIndexSQL = `
      CREATE INDEX IF NOT EXISTS idx_companies_bse_symbol ON companies(bse_symbol);
    `;
    await client.query(createIndexSQL);
    console.log("Created index on bse_symbol");

    console.log("Migration completed successfully");
  } catch (error) {
    console.error("Error during migration:", error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

addBseSymbolColumn();
