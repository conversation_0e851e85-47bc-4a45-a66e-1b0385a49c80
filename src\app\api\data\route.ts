// Simple proxy to websocket server - no caching, no complications
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);

    // Proxy directly to websocket server
    const wsServerUrl = `http://localhost:8080/api/data?${searchParams.toString()}`;
    const response = await fetch(wsServerUrl);

    if (!response.ok) {
      throw new Error(`WebSocket server responded with ${response.status}`);
    }

    const data = await response.json();
    return new Response(JSON.stringify(data), {
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("❌ Error connecting to market data server:", error);
    return new Response(
      JSON.stringify({
        error: "Market data server unavailable",
        connected: false,
        timestamp: Date.now(),
      }),
      {
        status: 503,
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  }
}
