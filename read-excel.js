const xlsx = require('xlsx');
const path = require('path');

// Read the Excel file
const filePath = path.join(__dirname, 'Companylist.xlsx');
console.log('Reading Excel file:', filePath);

try {
    const workbook = xlsx.readFile(filePath);
    console.log('Sheet names:', workbook.SheetNames);
    
    // Read the first sheet
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = xlsx.utils.sheet_to_json(worksheet);
    
    console.log(`\nTotal rows: ${data.length}`);
    console.log('\nFirst 10 rows:');
    console.log(JSON.stringify(data.slice(0, 10), null, 2));
    
    // Show column headers
    if (data.length > 0) {
        console.log('\nColumn headers:');
        console.log(Object.keys(data[0]));
    }
    
    // Show some statistics
    console.log('\nSample data analysis:');
    const sampleRow = data[0];
    if (sampleRow) {
        Object.keys(sampleRow).forEach(key => {
            console.log(`${key}: ${sampleRow[key]}`);
        });
    }
    
} catch (error) {
    console.error('Error reading Excel file:', error.message);
}
