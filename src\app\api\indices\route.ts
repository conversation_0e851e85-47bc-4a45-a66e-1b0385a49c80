export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);

    // Proxy to the WebSocket server
    const wsServerUrl = `http://localhost:8080/api/indices?${searchParams.toString()}`;

    console.log("Proxying request to:", wsServerUrl);

    const response = await fetch(wsServerUrl);

    if (!response.ok) {
      throw new Error(`WebSocket server responded with ${response.status}`);
    }

    const data = await response.json();

    return new Response(JSON.stringify(data), {
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
      },
    });
  } catch (error) {
    console.error("Error proxying to WebSocket server:", error);

    // Return fallback mock data if the backend is not available
    const mockData = {
      connected: false,
      indices: [
        {
          ticker: "SENSEX",
          exchange: "IDX_I",
          exchangeCode: 0,
          securityId: "51",
          ltp: 82150.45,
          change: 245.3,
          changePercent: 0.3,
          volume: 125000,
          high: 82350.2,
          low: 81950.1,
          open: 82000.0,
          close: 81905.15,
          timestamp: Date.now(),
        },
        {
          ticker: "NIFTY",
          exchange: "IDX_I",
          exchangeCode: 0,
          securityId: "13",
          ltp: 25125.8,
          change: 85.45,
          changePercent: 0.34,
          volume: 89000,
          high: 25200.3,
          low: 25050.2,
          open: 25100.0,
          close: 25040.35,
          timestamp: Date.now(),
        },
        {
          ticker: "BANKNIFTY",
          exchange: "IDX_I",
          exchangeCode: 0,
          securityId: "25",
          ltp: 52345.6,
          change: -125.4,
          changePercent: -0.24,
          volume: 45000,
          high: 52500.8,
          low: 52200.3,
          open: 52471.0,
          close: 52471.0,
          timestamp: Date.now(),
        },
        {
          ticker: "FINNIFTY",
          exchange: "IDX_I",
          exchangeCode: 0,
          securityId: "27",
          ltp: 24125.3,
          change: 45.2,
          changePercent: 0.19,
          volume: 32000,
          high: 24200.5,
          low: 24050.1,
          open: 24080.1,
          close: 24080.1,
          timestamp: Date.now(),
        },
        {
          ticker: "BANKEX",
          exchange: "IDX_I",
          exchangeCode: 0,
          securityId: "69",
          ltp: 45230.75,
          change: -85.25,
          changePercent: -0.19,
          volume: 28000,
          high: 45400.2,
          low: 45150.3,
          open: 45316.0,
          close: 45316.0,
          timestamp: Date.now(),
        },
      ],
      totalIndices: 101,
      activeIndices: 5,
    };

    return new Response(JSON.stringify(mockData), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
    });
  }
}
