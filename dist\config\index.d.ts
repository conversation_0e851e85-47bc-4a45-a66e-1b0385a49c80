export declare const appConfig: {
    database: {
        url: string;
        ssl: boolean;
        maxConnections: number;
        idleTimeout: number;
        connectionTimeout: number;
    };
    api: {
        port: number;
        accessToken: string;
        clientId: string;
    };
    websocket: {
        subscriptionType: "ticker" | "quote" | "full";
        maxInstruments: number;
        preferredExchange: "NSE" | "BSE";
        heartbeatInterval: number;
        reconnectAttempts: number;
        reconnectDelay: number;
    };
    security: {
        allowedOrigins: string[];
        rateLimitWindowMs: number;
        rateLimitMaxRequests: number;
    };
    upload: {
        maxFileSize: number;
        uploadDir: string;
        allowedExtensions: string[];
    };
    logging: {
        level: "error" | "warn" | "info" | "debug";
    };
};
//# sourceMappingURL=index.d.ts.map