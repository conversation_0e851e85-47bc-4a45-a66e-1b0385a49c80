export declare class DatabaseService {
    private static instance;
    private pool;
    private isInitialized;
    private constructor();
    static getInstance(): DatabaseService;
    initialize(): Promise<void>;
    private query;
    getCompanyBySymbol(symbol: string): Promise<any>;
    getCompanyByISIN(isin: string): Promise<any>;
    getCompanyBySecurityId(securityId: string): Promise<any>;
    searchCompanies(filters: any): Promise<any>;
    getSectors(): Promise<string[]>;
    getIndustries(sector?: string): Promise<string[]>;
    getSubSectors(industry?: string): Promise<string[]>;
    getMicroCategories(subSector?: string): Promise<string[]>;
    getCompaniesBySector(sector: string): Promise<any[]>;
    getCompaniesByIndustry(industry: string): Promise<any[]>;
    getCompaniesBySubSector(subSector: string): Promise<any[]>;
    getCompaniesByMicroCategory(microCategory: string): Promise<any[]>;
    getSectorDistribution(): Promise<any[]>;
    createWatchlist(name: string, type: string): Promise<number>;
    getWatchlists(): Promise<{
        id: number;
        name: string;
        type: string;
    }[]>;
    deleteWatchlist(id: number): Promise<void>;
    addToSectorWatchlist(watchlistId: number, sectorName: string): Promise<void>;
    getSectorWatchlist(watchlistId: number): Promise<string[]>;
    removeFromSectorWatchlist(watchlistId: number, sectorName: string): Promise<void>;
    addToIndustryWatchlist(watchlistId: number, industryName: string): Promise<void>;
    getIndustryWatchlist(watchlistId: number): Promise<string[]>;
    removeFromIndustryWatchlist(watchlistId: number, industryName: string): Promise<void>;
    addToSubSectorWatchlist(watchlistId: number, subSectorName: string): Promise<void>;
    getSubSectorWatchlist(watchlistId: number): Promise<string[]>;
    removeFromSubSectorWatchlist(watchlistId: number, subSectorName: string): Promise<void>;
    addToMicroCategoryWatchlist(watchlistId: number, microCategoryName: string): Promise<void>;
    getMicroCategoryWatchlist(watchlistId: number): Promise<string[]>;
    removeFromMicroCategoryWatchlist(watchlistId: number, microCategoryName: string): Promise<void>;
    healthCheck(): Promise<boolean>;
    getStats(): Promise<{
        watchlists: number;
        sectorWatchlist: number;
        industryWatchlist: number;
        subSectorWatchlist: number;
        microCategoryWatchlist: number;
    }>;
}
export default DatabaseService;
//# sourceMappingURL=database.d.ts.map